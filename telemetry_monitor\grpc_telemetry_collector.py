#!/usr/bin/env python3
"""
支持 gRPC 的 Telemetry 收集器
用于接收 Nokia 设备的 gRPC telemetry 数据流
"""

import socket
import json
import time
import threading
import os
import struct
from datetime import datetime
from collections import defaultdict

class GrpcTelemetryCollector:
    """支持 gRPC 的 Telemetry 收集器"""
    
    def __init__(self, host='0.0.0.0', port=57400, data_dir='telemetry_data'):
        self.host = host
        self.port = port
        self.data_dir = data_dir
        self.running = False
        self.server_socket = None
        self.client_connections = []
        self.total_data_count = 0
        self.start_time = None
        
        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 设备统计信息
        self.device_stats = defaultdict(lambda: {
            'message_count': 0,
            'last_seen': None,
            'first_seen': None,
            'data_file': None
        })
        
        # 全局统计
        self.active_devices = set()
    
    def start(self):
        """启动收集器"""
        
        print("🌟 gRPC Telemetry 收集器")
        print(f"📡 监听地址: {self.host}:{self.port}")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔍 等待设备连接...")
        print("=" * 80)
        
        try:
            # 创建服务器套接字
            print(f"🔧 创建服务器套接字...")
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            print(f"🔗 绑定到 {self.host}:{self.port}...")
            self.server_socket.bind((self.host, self.port))

            print(f"👂 开始监听，队列大小: 20...")
            self.server_socket.listen(20)

            print(f"✅ 服务器套接字创建成功")
            
            self.running = True
            self.start_time = datetime.now()
            
            # 启动状态报告线程
            status_thread = threading.Thread(target=self._status_reporter)
            status_thread.daemon = True
            status_thread.start()
            
            while self.running:
                try:
                    # 接受连接
                    client_socket, client_address = self.server_socket.accept()
                    print(f"\n🔗 新连接: {client_address}")
                    print(f"⏰ 连接时间: {datetime.now().strftime('%H:%M:%S')}")
                    
                    self.client_connections.append(client_socket)
                    
                    # 为每个客户端启动处理线程
                    client_thread = threading.Thread(
                        target=self._handle_grpc_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ 接受连接失败: {e}")
                    
        except Exception as e:
            print(f"❌ 启动收集器失败: {e}")
        
        finally:
            try:
                if self.server_socket:
                    self.server_socket.close()
            except:
                pass
    
    def _handle_grpc_client(self, client_socket, client_address):
        """处理 gRPC 客户端数据"""
        
        buffer = b""
        
        try:
            # 设置超时
            client_socket.settimeout(30.0)
            
            while self.running:
                try:
                    data = client_socket.recv(8192)
                    if not data:
                        print(f"📡 客户端 {client_address} 发送了空数据，连接可能关闭")
                        break
                    
                    buffer += data
                    print(f"📦 从 {client_address} 接收到 {len(data)} 字节数据")
                    
                    # 尝试解析 gRPC 消息
                    while len(buffer) >= 5:  # gRPC 消息至少需要5字节的头部
                        # gRPC 消息格式: [压缩标志(1字节)] + [消息长度(4字节)] + [消息内容]
                        compressed = buffer[0]
                        message_length = struct.unpack('>I', buffer[1:5])[0]
                        
                        if len(buffer) < 5 + message_length:
                            # 消息不完整，等待更多数据
                            break
                        
                        # 提取完整消息
                        message_data = buffer[5:5+message_length]
                        buffer = buffer[5+message_length:]
                        
                        print(f"📋 解析到完整 gRPC 消息: 压缩={compressed}, 长度={message_length}")
                        
                        # 处理消息
                        self._process_grpc_message(message_data, client_address, compressed)
                
                except socket.timeout:
                    print(f"⏰ 客户端 {client_address} 超时，继续等待...")
                    continue
                except Exception as e:
                    print(f"❌ 处理客户端 {client_address} 数据时出错: {e}")
                    break
        
        except Exception as e:
            print(f"❌ 处理客户端 {client_address} 失败: {e}")
        
        finally:
            try:
                client_socket.close()
                if client_socket in self.client_connections:
                    self.client_connections.remove(client_socket)
                print(f"🔌 客户端 {client_address} 断开连接")
            except:
                pass
    
    def _process_grpc_message(self, message_data, client_address, compressed):
        """处理 gRPC 消息"""
        
        self.total_data_count += 1
        timestamp = datetime.now()
        
        try:
            # 尝试将消息数据转换为可读格式
            if compressed:
                print(f"⚠️ 收到压缩消息，暂不支持解压缩")
                message_text = f"<compressed_data_length_{len(message_data)}>"
            else:
                try:
                    # 尝试解码为文本
                    message_text = message_data.decode('utf-8', errors='replace')
                except:
                    # 如果不是文本，显示十六进制
                    message_text = message_data.hex()
            
            # 确定设备名称
            device_name = self._get_device_name(client_address[0])
            
            # 更新设备统计
            device_stat = self.device_stats[device_name]
            device_stat['message_count'] += 1
            device_stat['last_seen'] = timestamp
            if device_stat['first_seen'] is None:
                device_stat['first_seen'] = timestamp
            
            self.active_devices.add(device_name)
            
            # 保存数据
            self._save_message_data(device_name, {
                'timestamp': timestamp.isoformat(),
                'source_ip': client_address[0],
                'source_port': client_address[1],
                'message_length': len(message_data),
                'compressed': compressed,
                'data': message_text,
                'raw_hex': message_data.hex()[:200] + ('...' if len(message_data) > 100 else '')
            })
            
            print(f"✅ 处理消息: 设备={device_name}, 长度={len(message_data)}, 压缩={compressed}")
            
        except Exception as e:
            print(f"❌ 处理 gRPC 消息失败: {e}")
    
    def _get_device_name(self, ip_address):
        """根据IP地址获取设备名称"""
        device_mapping = {
            '***************': 'nokia-alu-1',
            '***************': 'nokia-alu-2', 
            '***************': 'nokia-alu-3',
            '***************': 'nokia-alu-4'
        }
        return device_mapping.get(ip_address, f'unknown-{ip_address}')
    
    def _save_message_data(self, device_name, data):
        """保存消息数据到文件"""
        try:
            date_str = datetime.now().strftime('%Y%m%d')
            filename = f"{self.data_dir}/{device_name}_{date_str}.json"
            
            # 确保设备统计中有文件名
            if self.device_stats[device_name]['data_file'] != filename:
                self.device_stats[device_name]['data_file'] = filename
            
            with open(filename, 'a', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
                f.write('\n')
                
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def _status_reporter(self):
        """状态报告线程"""
        while self.running:
            try:
                time.sleep(30)  # 每30秒报告一次状态
                if self.running:
                    self._print_status()
            except:
                pass
    
    def _print_status(self):
        """打印状态信息"""
        print(f"\n📊 === 状态报告 ({datetime.now().strftime('%H:%M:%S')}) ===")
        print(f"🔗 活跃连接: {len(self.client_connections)}")
        print(f"📦 总消息数: {self.total_data_count}")
        print(f"📱 活跃设备: {len(self.active_devices)}")
        
        if self.active_devices:
            for device in sorted(self.active_devices):
                stat = self.device_stats[device]
                print(f"   {device}: {stat['message_count']} 消息")
    
    def stop(self):
        """停止收集器"""
        print("\n🛑 正在停止收集器...")
        self.running = False
        
        # 关闭所有客户端连接
        for client_socket in self.client_connections[:]:
            try:
                client_socket.close()
            except:
                pass
        
        # 关闭服务器套接字
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        print("✅ 收集器已停止")

def main():
    """主函数"""
    collector = GrpcTelemetryCollector()
    
    try:
        collector.start()
    except KeyboardInterrupt:
        print("\n⚠️ 收到中断信号")
    finally:
        collector.stop()

if __name__ == "__main__":
    main()
