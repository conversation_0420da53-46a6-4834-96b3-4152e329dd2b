#!/usr/bin/env python3
"""
工具函数测试
"""

import unittest
import tempfile
import os
import sys
import json
import yaml

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.utils import (
    validate_ip_address, validate_port, format_xml, parse_xml_response,
    xml_to_dict, save_config_to_file, load_config_from_file,
    format_bytes, format_duration, generate_unique_id,
    validate_telemetry_config, create_backup_filename,
    ensure_directory_exists, truncate_string, print_table
)


class TestValidationFunctions(unittest.TestCase):
    """验证函数测试类"""
    
    def test_validate_ip_address(self):
        """测试 IP 地址验证"""
        # 有效的 IP 地址
        valid_ips = [
            '***********',
            '********',
            '**********',
            '127.0.0.1',
            '***************',
            '0.0.0.0'
        ]
        
        for ip in valid_ips:
            self.assertTrue(validate_ip_address(ip), f"IP {ip} 应该是有效的")
        
        # 无效的 IP 地址
        invalid_ips = [
            '256.1.1.1',
            '192.168.1',
            '***********.1',
            'invalid',
            '',
            '192.168.1.-1'
        ]
        
        for ip in invalid_ips:
            self.assertFalse(validate_ip_address(ip), f"IP {ip} 应该是无效的")
    
    def test_validate_port(self):
        """测试端口验证"""
        # 有效端口
        valid_ports = [1, 80, 443, 8080, 65535]
        for port in valid_ports:
            self.assertTrue(validate_port(port), f"端口 {port} 应该是有效的")
        
        # 无效端口
        invalid_ports = [0, -1, 65536, 100000]
        for port in invalid_ports:
            self.assertFalse(validate_port(port), f"端口 {port} 应该是无效的")


class TestXmlFunctions(unittest.TestCase):
    """XML 处理函数测试类"""
    
    def test_format_xml(self):
        """测试 XML 格式化"""
        xml_string = '<root><child>value</child></root>'
        formatted = format_xml(xml_string)
        
        self.assertIn('<root>', formatted)
        self.assertIn('<child>value</child>', formatted)
        self.assertIn('</root>', formatted)
    
    def test_format_xml_invalid(self):
        """测试无效 XML 格式化"""
        invalid_xml = '<root><child>value</root>'  # 缺少结束标签
        result = format_xml(invalid_xml)
        self.assertEqual(result, invalid_xml)  # 应该返回原始字符串
    
    def test_parse_xml_response(self):
        """测试 XML 响应解析"""
        xml_string = '<root><name>test</name><value>123</value></root>'
        result = parse_xml_response(xml_string)
        
        self.assertIsInstance(result, dict)
        self.assertEqual(result['name'], 'test')
        self.assertEqual(result['value'], '123')
    
    def test_parse_xml_response_invalid(self):
        """测试无效 XML 响应解析"""
        invalid_xml = '<root><name>test</root>'  # 无效 XML
        result = parse_xml_response(invalid_xml)
        
        self.assertIn('error', result)
    
    def test_xml_to_dict(self):
        """测试 XML 转字典"""
        import xml.etree.ElementTree as ET
        
        xml_string = '<root attr="value"><child>text</child></root>'
        element = ET.fromstring(xml_string)
        result = xml_to_dict(element)
        
        self.assertIsInstance(result, dict)
        self.assertEqual(result['@attributes']['attr'], 'value')
        self.assertEqual(result['child'], 'text')


class TestFileOperations(unittest.TestCase):
    """文件操作测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_save_and_load_yaml_config(self):
        """测试保存和加载 YAML 配置"""
        config = {
            'name': 'test',
            'value': 123,
            'list': [1, 2, 3],
            'nested': {'key': 'value'}
        }
        
        file_path = os.path.join(self.temp_dir, 'test.yaml')
        save_config_to_file(config, file_path)
        
        self.assertTrue(os.path.exists(file_path))
        
        loaded_config = load_config_from_file(file_path)
        self.assertEqual(config, loaded_config)
    
    def test_save_and_load_json_config(self):
        """测试保存和加载 JSON 配置"""
        config = {
            'name': 'test',
            'value': 123,
            'list': [1, 2, 3],
            'nested': {'key': 'value'}
        }
        
        file_path = os.path.join(self.temp_dir, 'test.json')
        save_config_to_file(config, file_path)
        
        self.assertTrue(os.path.exists(file_path))
        
        loaded_config = load_config_from_file(file_path)
        self.assertEqual(config, loaded_config)
    
    def test_load_nonexistent_file(self):
        """测试加载不存在的文件"""
        with self.assertRaises(FileNotFoundError):
            load_config_from_file('/nonexistent/file.yaml')
    
    def test_ensure_directory_exists(self):
        """测试确保目录存在"""
        test_dir = os.path.join(self.temp_dir, 'new_dir', 'sub_dir')
        self.assertFalse(os.path.exists(test_dir))
        
        ensure_directory_exists(test_dir)
        self.assertTrue(os.path.exists(test_dir))


class TestFormatFunctions(unittest.TestCase):
    """格式化函数测试类"""
    
    def test_format_bytes(self):
        """测试字节格式化"""
        test_cases = [
            (0, '0.00 B'),
            (1024, '1.00 KB'),
            (1024 * 1024, '1.00 MB'),
            (1024 * 1024 * 1024, '1.00 GB'),
            (1024 * 1024 * 1024 * 1024, '1.00 TB')
        ]
        
        for bytes_value, expected in test_cases:
            result = format_bytes(bytes_value)
            self.assertEqual(result, expected)
    
    def test_format_duration(self):
        """测试持续时间格式化"""
        test_cases = [
            (30, '30秒'),
            (90, '1分30秒'),
            (3661, '1小时1分钟'),
            (90061, '1天1小时')
        ]
        
        for seconds, expected in test_cases:
            result = format_duration(seconds)
            self.assertEqual(result, expected)
    
    def test_truncate_string(self):
        """测试字符串截断"""
        long_string = "这是一个很长的字符串用于测试截断功能"
        
        # 测试正常截断
        result = truncate_string(long_string, 10)
        self.assertEqual(len(result), 10)
        self.assertTrue(result.endswith('...'))
        
        # 测试不需要截断
        short_string = "短字符串"
        result = truncate_string(short_string, 20)
        self.assertEqual(result, short_string)


class TestUtilityFunctions(unittest.TestCase):
    """工具函数测试类"""
    
    def test_generate_unique_id(self):
        """测试生成唯一 ID"""
        # 无前缀
        id1 = generate_unique_id()
        id2 = generate_unique_id()
        self.assertNotEqual(id1, id2)
        self.assertIsInstance(id1, str)
        
        # 有前缀
        id_with_prefix = generate_unique_id('test')
        self.assertTrue(id_with_prefix.startswith('test_'))
    
    def test_create_backup_filename(self):
        """测试创建备份文件名"""
        original = '/path/to/config.yaml'
        backup = create_backup_filename(original)
        
        self.assertTrue(backup.startswith('/path/to/config_backup_'))
        self.assertTrue(backup.endswith('.yaml'))
        self.assertNotEqual(original, backup)
    
    def test_validate_telemetry_config(self):
        """测试 Telemetry 配置验证"""
        # 有效配置
        valid_config = {
            'collectors': [
                {
                    'name': 'test-collector',
                    'address': '*************',
                    'port': 57400
                }
            ],
            'monitoring_templates': {
                'test_template': {
                    'categories': ['system_basic'],
                    'sample_interval': 30000
                }
            }
        }
        
        errors = validate_telemetry_config(valid_config)
        self.assertEqual(len(errors), 0)
        
        # 无效配置
        invalid_config = {
            'collectors': [
                {
                    'name': 'test-collector',
                    'address': 'invalid-ip',  # 无效 IP
                    'port': 70000  # 无效端口
                }
            ],
            'monitoring_templates': {
                'test_template': {
                    # 缺少 categories
                    'sample_interval': -1000  # 无效间隔
                }
            }
        }
        
        errors = validate_telemetry_config(invalid_config)
        self.assertGreater(len(errors), 0)


class TestProgressBar(unittest.TestCase):
    """进度条测试类"""
    
    def test_progress_bar_creation(self):
        """测试进度条创建"""
        from src.utils import ProgressBar
        
        progress = ProgressBar(100, "测试进度")
        self.assertEqual(progress.total, 100)
        self.assertEqual(progress.current, 0)
        self.assertEqual(progress.description, "测试进度")
    
    def test_progress_bar_update(self):
        """测试进度条更新"""
        from src.utils import ProgressBar
        
        progress = ProgressBar(10, "测试进度")
        
        progress.update(5)
        self.assertEqual(progress.current, 5)
        
        progress.update(3)
        self.assertEqual(progress.current, 8)
        
        progress.finish()
        self.assertEqual(progress.current, 10)


if __name__ == '__main__':
    unittest.main()