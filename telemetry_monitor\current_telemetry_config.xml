<rpc-reply message-id="urn:uuid:0c645360-2851-4b04-a888-0c888954d353">
  <data>
    <telemetry-system>
      <sensor-groups>
        <sensor-group>
          <sensor-group-id>allSg</sensor-group-id>
          <config>
            <sensor-group-id>allSg</sensor-group-id>
          </config>
          <sensor-paths>
            <sensor-path>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
              <config>
                <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
              </config>
            </sensor-path>
            <sensor-path>
              <path>/openconfig-platform:components/component/state</path>
              <config>
                <path>/openconfig-platform:components/component/state</path>
              </config>
            </sensor-path>
            <sensor-path>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
              <config>
                <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
              </config>
            </sensor-path>
            <sensor-path>
              <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
              <config>
                <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
              </config>
            </sensor-path>
            <sensor-path>
              <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
              <config>
                <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
              </config>
            </sensor-path>
            <sensor-path>
              <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
              <config>
                <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
              </config>
            </sensor-path>
            <sensor-path>
              <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
              <config>
                <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
              </config>
            </sensor-path>
          </sensor-paths>
        </sensor-group>
      </sensor-groups>
      <destination-groups>
        <destination-group>
          <group-id>test-collector</group-id>
          <destinations>
            <destination>
              <destination-address>***********</destination-address>
              <destination-port>57400</destination-port>
              <config>
                <destination-port>57400</destination-port>
                <destination-address>***********</destination-address>
              </config>
            </destination>
          </destinations>
          <config>
            <group-id>test-collector</group-id>
          </config>
        </destination-group>
        <destination-group>
          <group-id>optical-collector</group-id>
          <destinations>
            <destination>
              <destination-address>***************</destination-address>
              <destination-port>57400</destination-port>
              <config>
                <destination-port>57400</destination-port>
                <destination-address>***************</destination-address>
              </config>
            </destination>
          </destinations>
          <config>
            <group-id>optical-collector</group-id>
          </config>
        </destination-group>
        <destination-group>
          <group-id>comprehensive-collector</group-id>
          <destinations>
            <destination>
              <destination-address>***************</destination-address>
              <destination-port>57400</destination-port>
              <config>
                <destination-port>57400</destination-port>
                <destination-address>***************</destination-address>
              </config>
            </destination>
          </destinations>
          <config>
            <group-id>comprehensive-collector</group-id>
          </config>
        </destination-group>
      </destination-groups>
    </telemetry-system>
  </data>
</rpc-reply>
