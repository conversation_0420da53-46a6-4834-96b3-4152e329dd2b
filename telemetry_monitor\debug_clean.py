#!/usr/bin/env python3
"""
调试清理功能的脚本
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

from src.netconf_client import NetconfClient
from src.utils import setup_logging

def test_clean_operations():
    """测试不同的清理操作"""
    
    # 设置日志
    setup_logging("INFO")
    
    # 创建客户端
    client = NetconfClient("config/device_config.yaml")
    device_name = "nokia-alu-1"
    
    print(f"🔍 开始调试清理操作 - 设备: {device_name}")
    
    try:
        with client.get_connection(device_name) as conn:
            # 首先获取当前配置
            print("\n1. 获取当前配置...")
            filter_xml = '''
            <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
              <sensor-groups/>
              <destination-groups/>
              <subscriptions/>
            </telemetry-system>
            '''
            
            current_config = conn.get_config(source='running', filter=('subtree', filter_xml))
            print("当前配置:")
            print(str(current_config))
            
            # 尝试不同的删除方法
            print("\n2. 尝试删除整个 telemetry-system...")
            try:
                delete_all = '''
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry" operation="delete"/>
</config>
'''
                result = conn.edit_config(target='running', config=delete_all)
                print("✅ 删除整个 telemetry-system 成功")
                print(f"结果: {result}")
            except Exception as e:
                print(f"❌ 删除整个 telemetry-system 失败: {e}")
            
            # 检查删除后的状态
            print("\n3. 检查删除后的配置...")
            try:
                after_config = conn.get_config(source='running', filter=('subtree', filter_xml))
                print("删除后配置:")
                print(str(after_config))
            except Exception as e:
                print(f"获取删除后配置失败: {e}")
                
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
    
    finally:
        try:
            client.disconnect_all()
        except:
            pass

if __name__ == "__main__":
    test_clean_operations()
