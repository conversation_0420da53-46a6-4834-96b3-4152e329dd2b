#!/usr/bin/env python3
"""
OpenConfig Telemetry Monitor 主程序
基于 ncclient 和 OpenConfig YANG 模型的网络设备 Telemetry 配置工具
"""

import os
import sys
import click
import logging
from typing import List, Optional

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.telemetry_config import TelemetryConfigurator
from src.utils import setup_logging, print_table, ProgressBar, validate_ip_address, validate_port


# 全局配置
DEFAULT_DEVICE_CONFIG = "config/device_config.yaml"
DEFAULT_TELEMETRY_CONFIG = "config/telemetry_config.yaml"
DEFAULT_LOG_LEVEL = "INFO"


@click.group()
@click.option('--log-level', default=DEFAULT_LOG_LEVEL, 
              type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']),
              help='日志级别')
@click.option('--log-file', help='日志文件路径')
@click.option('--device-config', default=DEFAULT_DEVICE_CONFIG, 
              help='设备配置文件路径')
@click.option('--telemetry-config', default=DEFAULT_TELEMETRY_CONFIG,
              help='Telemetry 配置文件路径')
@click.pass_context
def cli(ctx, log_level, log_file, device_config, telemetry_config):
    """OpenConfig Telemetry Monitor - 网络设备 Telemetry 配置工具"""
    # 设置日志
    setup_logging(log_level, log_file)
    
    # 创建上下文对象
    ctx.ensure_object(dict)
    ctx.obj['device_config'] = device_config
    ctx.obj['telemetry_config'] = telemetry_config
    ctx.obj['log_level'] = log_level


@cli.command()
@click.pass_context
def list_devices(ctx):
    """列出所有配置的设备"""
    try:
        configurator = TelemetryConfigurator(
            device_config_file=ctx.obj['device_config'],
            telemetry_config_file=ctx.obj['telemetry_config']
        )
        
        devices = configurator.netconf_client.list_devices()
        
        if not devices:
            click.echo("未找到配置的设备")
            return
        
        # 获取设备详细信息
        device_info = []
        for device_name in devices:
            info = configurator.netconf_client.get_device_info(device_name)
            device_info.append(info)
        
        # 打印设备表格
        headers = ['name', 'host', 'port', 'username', 'device_type', 'connected']
        print_table(device_info, headers)
        
    except Exception as e:
        click.echo(f"错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('device_name')
@click.pass_context
def test_connection(ctx, device_name):
    """测试设备连接"""
    try:
        configurator = TelemetryConfigurator(
            device_config_file=ctx.obj['device_config'],
            telemetry_config_file=ctx.obj['telemetry_config']
        )
        
        click.echo(f"正在测试设备 '{device_name}' 的连接...")
        
        if configurator.netconf_client.validate_connection(device_name):
            click.echo(f"✓ 设备 '{device_name}' 连接成功", color='green')
            
            # 显示设备能力
            capabilities = configurator.netconf_client.get_device_capabilities(device_name)
            click.echo(f"设备支持 {len(capabilities)} 个 NETCONF 能力")
            
            # 检查 Telemetry 支持
            telemetry_supported = any('telemetry' in cap.lower() for cap in capabilities)
            if telemetry_supported:
                click.echo("✓ 设备支持 OpenConfig Telemetry", color='green')
            else:
                click.echo("⚠ 设备可能不支持 OpenConfig Telemetry", color='yellow')
        else:
            click.echo(f"✗ 设备 '{device_name}' 连接失败", color='red')
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def list_templates(ctx):
    """列出所有可用的监控模板"""
    try:
        configurator = TelemetryConfigurator(
            device_config_file=ctx.obj['device_config'],
            telemetry_config_file=ctx.obj['telemetry_config']
        )
        
        templates = configurator.list_available_templates()
        
        if not templates:
            click.echo("未找到监控模板")
            return
        
        click.echo("可用的监控模板:")
        for template_name in templates:
            template_info = configurator.get_template_info(template_name)
            click.echo(f"\n模板: {template_name}")
            click.echo(f"  描述: {template_info['description']}")
            click.echo(f"  类别: {', '.join(template_info['categories'])}")
            click.echo(f"  采样间隔: {template_info['sample_interval']}ms")
            click.echo(f"  总指标数: {template_info['total_metrics']}")
        
    except Exception as e:
        click.echo(f"错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def list_categories(ctx):
    """列出所有可用的监控类别"""
    try:
        configurator = TelemetryConfigurator(
            device_config_file=ctx.obj['device_config'],
            telemetry_config_file=ctx.obj['telemetry_config']
        )
        
        categories = configurator.list_available_categories()
        
        if not categories:
            click.echo("未找到监控类别")
            return
        
        click.echo("可用的监控类别:")
        for category in categories:
            metrics = configurator.monitoring_categories[category]
            click.echo(f"\n类别: {category}")
            click.echo(f"  指标: {', '.join(metrics)}")
        
    except Exception as e:
        click.echo(f"错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('device_name')
@click.option('--template', default='basic', help='监控模板名称')
@click.option('--collector', help='收集器地址 (格式: IP:PORT)')
@click.option('--interval', type=int, help='采样间隔（毫秒）')
@click.option('--dry-run', is_flag=True, help='仅显示配置，不实际应用')
@click.pass_context
def configure(ctx, device_name, template, collector, interval, dry_run):
    """配置设备的 Telemetry 监控"""
    try:
        configurator = TelemetryConfigurator(
            device_config_file=ctx.obj['device_config'],
            telemetry_config_file=ctx.obj['telemetry_config']
        )
        
        # 处理收集器配置
        collector_name = "default-collector"
        if collector:
            # 解析收集器地址
            if ':' in collector:
                collector_ip, collector_port = collector.split(':', 1)
                collector_port = int(collector_port)
            else:
                collector_ip = collector
                collector_port = 57400
            
            # 验证 IP 地址和端口
            if not validate_ip_address(collector_ip):
                click.echo(f"错误: 无效的 IP 地址 '{collector_ip}'", err=True)
                sys.exit(1)
            
            if not validate_port(collector_port):
                click.echo(f"错误: 无效的端口号 '{collector_port}'", err=True)
                sys.exit(1)
            
            # 添加收集器
            collector_name = f"custom_{device_name}"
            configurator.add_collector(collector_name, collector_ip, collector_port)
        
        if dry_run:
            click.echo(f"配置预览 - 设备: {device_name}")
            click.echo(f"模板: {template}")
            click.echo(f"收集器: {collector_name}")
            if interval:
                click.echo(f"采样间隔: {interval}ms")
            
            # 显示将要创建的传感器组
            template_info = configurator.get_template_info(template)
            click.echo(f"\n将创建的监控类别:")
            for category in template_info['categories']:
                if category in configurator.monitoring_categories:
                    metrics = configurator.monitoring_categories[category]
                    click.echo(f"  {category}: {len(metrics)} 个指标")
            return
        
        # 执行配置
        click.echo(f"正在配置设备 '{device_name}' 的 Telemetry 监控...")
        
        success = configurator.configure_telemetry(
            device_name=device_name,
            template_name=template,
            collector_name=collector_name,
            sample_interval=interval
        )
        
        if success:
            click.echo(f"✓ 设备 '{device_name}' Telemetry 配置成功", color='green')
        else:
            click.echo(f"✗ 设备 '{device_name}' Telemetry 配置失败", color='red')
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('device_name')
@click.option('--categories', required=True, help='监控类别列表（逗号分隔）')
@click.option('--collector', required=True, help='收集器地址 (格式: IP:PORT)')
@click.option('--interval', type=int, default=30000, help='采样间隔（毫秒）')
@click.option('--dry-run', is_flag=True, help='仅显示配置，不实际应用')
@click.pass_context
def configure_custom(ctx, device_name, categories, collector, interval, dry_run):
    """使用自定义类别配置设备的 Telemetry 监控"""
    try:
        configurator = TelemetryConfigurator(
            device_config_file=ctx.obj['device_config'],
            telemetry_config_file=ctx.obj['telemetry_config']
        )
        
        # 解析类别列表
        category_list = [cat.strip() for cat in categories.split(',')]
        
        # 验证类别
        available_categories = configurator.list_available_categories()
        invalid_categories = [cat for cat in category_list if cat not in available_categories]
        if invalid_categories:
            click.echo(f"错误: 无效的监控类别: {', '.join(invalid_categories)}", err=True)
            click.echo(f"可用类别: {', '.join(available_categories)}")
            sys.exit(1)
        
        # 解析收集器地址
        if ':' in collector:
            collector_ip, collector_port = collector.split(':', 1)
            collector_port = int(collector_port)
        else:
            collector_ip = collector
            collector_port = 57400
        
        # 验证 IP 地址和端口
        if not validate_ip_address(collector_ip):
            click.echo(f"错误: 无效的 IP 地址 '{collector_ip}'", err=True)
            sys.exit(1)
        
        if not validate_port(collector_port):
            click.echo(f"错误: 无效的端口号 '{collector_port}'", err=True)
            sys.exit(1)
        
        if dry_run:
            click.echo(f"自定义配置预览 - 设备: {device_name}")
            click.echo(f"监控类别: {', '.join(category_list)}")
            click.echo(f"收集器: {collector_ip}:{collector_port}")
            click.echo(f"采样间隔: {interval}ms")
            
            # 显示指标详情
            total_metrics = 0
            for category in category_list:
                metrics = configurator.monitoring_categories[category]
                click.echo(f"  {category}: {len(metrics)} 个指标")
                total_metrics += len(metrics)
            click.echo(f"总计: {total_metrics} 个监控指标")
            return
        
        # 执行配置
        click.echo(f"正在配置设备 '{device_name}' 的自定义 Telemetry 监控...")
        
        success = configurator.configure_custom_telemetry(
            device_name=device_name,
            categories=category_list,
            collector_address=collector_ip,
            collector_port=collector_port,
            sample_interval=interval
        )
        
        if success:
            click.echo(f"✓ 设备 '{device_name}' 自定义 Telemetry 配置成功", color='green')
        else:
            click.echo(f"✗ 设备 '{device_name}' 自定义 Telemetry 配置失败", color='red')
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('device_name')
@click.pass_context
def status(ctx, device_name):
    """查看设备 Telemetry 状态"""
    try:
        configurator = TelemetryConfigurator(
            device_config_file=ctx.obj['device_config'],
            telemetry_config_file=ctx.obj['telemetry_config']
        )
        
        click.echo(f"正在获取设备 '{device_name}' 的 Telemetry 状态...")
        
        status_info = configurator.get_telemetry_status(device_name)
        
        click.echo(f"\n设备: {status_info['device']}")
        if status_info.get('configured'):
            click.echo(f"✓ Telemetry 已配置", color='green')
            click.echo(f"传感器组数量: {status_info.get('sensor_groups', 0)}")
            click.echo(f"订阅数量: {status_info.get('subscriptions', 0)}")
            click.echo(f"收集器数量: {status_info.get('collectors', 0)}")
        else:
            click.echo(f"✗ Telemetry 未配置或配置有误", color='red')
            if 'error' in status_info:
                click.echo(f"错误: {status_info['error']}")
        
    except Exception as e:
        click.echo(f"错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('device_name')
@click.pass_context
def validate(ctx, device_name):
    """验证设备配置"""
    try:
        configurator = TelemetryConfigurator(
            device_config_file=ctx.obj['device_config'],
            telemetry_config_file=ctx.obj['telemetry_config']
        )
        
        click.echo(f"正在验证设备 '{device_name}' 的配置...")
        
        results = configurator.validate_configuration(device_name)
        
        click.echo(f"\n验证结果:")
        
        # 设备连接
        if results['device_connection']:
            click.echo("✓ 设备连接正常", color='green')
        else:
            click.echo("✗ 设备连接失败", color='red')
        
        # Telemetry 支持
        if results['telemetry_support']:
            click.echo("✓ 设备支持 Telemetry", color='green')
        else:
            click.echo("✗ 设备不支持 Telemetry", color='red')
        
        # 配置有效性
        if results['configuration_valid']:
            click.echo("✓ 配置有效", color='green')
        else:
            click.echo("✗ 配置无效", color='red')
        
        # 显示错误信息
        if results['errors']:
            click.echo("\n错误信息:")
            for error in results['errors']:
                click.echo(f"  • {error}", color='red')
        
        # 返回适当的退出码
        if not all([results['device_connection'], results['telemetry_support'], results['configuration_valid']]):
            sys.exit(1)
        
    except Exception as e:
        click.echo(f"错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--devices', help='设备名称列表（逗号分隔），留空表示所有设备')
@click.option('--template', default='basic', help='监控模板名称')
@click.option('--collector', required=True, help='收集器地址 (格式: IP:PORT)')
@click.option('--interval', type=int, help='采样间隔（毫秒）')
@click.option('--parallel', is_flag=True, help='并行配置多个设备')
@click.pass_context
def batch_configure(ctx, devices, template, collector, interval, parallel):
    """批量配置多个设备的 Telemetry 监控"""
    try:
        configurator = TelemetryConfigurator(
            device_config_file=ctx.obj['device_config'],
            telemetry_config_file=ctx.obj['telemetry_config']
        )
        
        # 确定要配置的设备列表
        if devices:
            device_list = [dev.strip() for dev in devices.split(',')]
        else:
            device_list = configurator.netconf_client.list_devices()
        
        if not device_list:
            click.echo("未找到要配置的设备")
            return
        
        # 解析收集器地址
        if ':' in collector:
            collector_ip, collector_port = collector.split(':', 1)
            collector_port = int(collector_port)
        else:
            collector_ip = collector
            collector_port = 57400
        
        # 验证 IP 地址和端口
        if not validate_ip_address(collector_ip):
            click.echo(f"错误: 无效的 IP 地址 '{collector_ip}'", err=True)
            sys.exit(1)
        
        if not validate_port(collector_port):
            click.echo(f"错误: 无效的端口号 '{collector_port}'", err=True)
            sys.exit(1)
        
        click.echo(f"正在批量配置 {len(device_list)} 个设备...")
        click.echo(f"模板: {template}")
        click.echo(f"收集器: {collector_ip}:{collector_port}")
        
        # 添加收集器
        collector_name = "batch_collector"
        configurator.add_collector(collector_name, collector_ip, collector_port)
        
        # 配置进度条
        progress = ProgressBar(len(device_list), "配置进度")
        
        success_count = 0
        failed_devices = []
        
        for device_name in device_list:
            try:
                success = configurator.configure_telemetry(
                    device_name=device_name,
                    template_name=template,
                    collector_name=collector_name,
                    sample_interval=interval
                )
                
                if success:
                    success_count += 1
                else:
                    failed_devices.append(device_name)
                    
            except Exception as e:
                logging.error(f"配置设备 '{device_name}' 失败: {e}")
                failed_devices.append(device_name)
            
            progress.update()
        
        progress.finish()
        
        # 显示结果
        click.echo(f"\n批量配置完成:")
        click.echo(f"✓ 成功: {success_count} 个设备", color='green')
        
        if failed_devices:
            click.echo(f"✗ 失败: {len(failed_devices)} 个设备", color='red')
            click.echo(f"失败的设备: {', '.join(failed_devices)}")
            sys.exit(1)
        
    except Exception as e:
        click.echo(f"错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def version(ctx):
    """显示版本信息"""
    click.echo("OpenConfig Telemetry Monitor v1.0.0")
    click.echo("基于 ncclient 和 OpenConfig YANG 模型的网络设备 Telemetry 配置工具")


def main():
    """主函数"""
    try:
        cli()
    except KeyboardInterrupt:
        click.echo("\n操作已取消", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"未预期的错误: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    main()