<rpc message-id="101" xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
	<edit-config>
		<target>
			<running/>
		</target>
		<config xmlns:xc="urn:ietf:params:xml:ns:netconf:base:1.0">
			<telemetry-system xmlns="http://openconfig.net/yang/telemetry" xc:operation="create">
				<destination-groups>
					<destination-group>
						<group-id>************</group-id>
						<config>
							<group-id>************</group-id>
						</config>
						<destinations>
							<destination>
								<destination-address>************</destination-address>
								<destination-port>50051</destination-port>
								<config>
									<destination-address>************</destination-address>
									<destination-port>50051</destination-port>
								</config>
							</destination>
						</destinations>
					</destination-group>
				</destination-groups>
				<sensor-groups>
					<sensor-group>
						<sensor-group-id>allSg</sensor-group-id>
						<config>
							<sensor-group-id>allSg</sensor-group-id>
						</config>
						<sensor-paths>
							<sensor-path>
								<path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
								<config>
									<path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
								</config>
							</sensor-path>					
							<sensor-path>
								<path>/openconfig-platform:components/component/state</path>
								<config>
									<path>/openconfig-platform:components/component/state</path>
								</config>
							</sensor-path>

							<sensor-path>
								<path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
								<state>
									<path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
								</state>
								<config>
									<path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
								</config>
							</sensor-path>

							<sensor-path>
								<path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
								<state>
									<path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
								</state>
								<config>
									<path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
								</config>
							</sensor-path>
							<sensor-path>
								<path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
								<state>
									<path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
								</state>
								<config>
									<path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
								</config>
							</sensor-path>	
							<sensor-path>
								<path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
								<state>
									<path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
								</state>
								<config>
									<path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
								</config>
							</sensor-path>		
							<sensor-path>
								<path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
								<config>
									<path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
								</config>
							</sensor-path>
							<sensor-path>
								<path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
								<config>
									<path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
								</config>
							</sensor-path>	
						</sensor-paths>
					</sensor-group>		
				</sensor-groups>		  
				<subscriptions>
					<persistent-subscriptions>
						<persistent-subscription>
							<name>allPsg</name>
							<config>
								<name>allPsg</name>
								<local-source-address>**************</local-source-address>
							</config>
							<sensor-profiles>
								<sensor-profile>
									<sensor-group>allSg</sensor-group>
									<config>
										<sensor-group>allSg</sensor-group>
										<sample-interval>5000</sample-interval>
										<heartbeat-interval>15000</heartbeat-interval>
										<suppress-redundant>false</suppress-redundant>
									</config>
								</sensor-profile>
							</sensor-profiles>
							<destination-groups>
								<destination-group>
									<group-id>************</group-id>
									<config>
										<group-id>************</group-id>
									</config>
								</destination-group>
							</destination-groups>
						</persistent-subscription>
					</persistent-subscriptions>
				</subscriptions>
			</telemetry-system>
		</config>
	</edit-config>
</rpc>

