#!/usr/bin/env python3
"""
OpenConfig Telemetry Monitor 源码包
基于 ncclient 和 OpenConfig YANG 模型的网络设备 Telemetry 配置工具
"""

__version__ = "1.0.0"
__author__ = "Telemetry Monitor Team"
__email__ = "<EMAIL>"
__description__ = "基于 OpenConfig YANG 模型的网络设备 Telemetry 配置工具"

# 导入主要类和函数
from .telemetry_config import TelemetryConfigurator, CollectorConfig, SensorGroupConfig, SubscriptionConfig
from .netconf_client import NetconfClient, DeviceConfig, ConnectionSettings
from .yang_templates import YangTemplates
from .utils import setup_logging, validate_ip_address, validate_port

__all__ = [
    # 主要类
    'TelemetryConfigurator',
    'NetconfClient', 
    'YangTemplates',
    
    # 配置类
    'CollectorConfig',
    'SensorGroupConfig', 
    'SubscriptionConfig',
    'DeviceConfig',
    'ConnectionSettings',
    
    # 工具函数
    'setup_logging',
    'validate_ip_address',
    'validate_port',
    
    # 版本信息
    '__version__',
    '__author__',
    '__email__',
    '__description__'
]