#!/usr/bin/env python3
"""
原始 gRPC Telemetry 收集器
处理 Nokia 设备的原始 gRPC 连接，不需要完整的 gRPC 框架
"""

import socket
import json
import time
import threading
import os
import struct
from datetime import datetime
from collections import defaultdict

class RawGrpcCollector:
    """原始 gRPC 收集器"""
    
    def __init__(self, host='0.0.0.0', port=57400, data_dir='telemetry_data'):
        self.host = host
        self.port = port
        self.data_dir = data_dir
        self.running = False
        self.server_socket = None
        self.client_connections = []
        self.total_data_count = 0
        self.start_time = None
        
        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 设备统计信息
        self.device_stats = defaultdict(lambda: {
            'message_count': 0,
            'last_seen': None,
            'first_seen': None,
            'connection_count': 0
        })
    
    def start(self):
        """启动收集器"""
        
        print("🌟 原始 gRPC Telemetry 收集器")
        print(f"📡 监听地址: {self.host}:{self.port}")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔍 等待设备连接...")
        print("=" * 80)
        
        try:
            # 创建服务器套接字
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(20)
            
            self.running = True
            self.start_time = datetime.now()
            
            # 启动状态报告线程
            status_thread = threading.Thread(target=self._status_reporter)
            status_thread.daemon = True
            status_thread.start()
            
            while self.running:
                try:
                    # 接受连接
                    client_socket, client_address = self.server_socket.accept()
                    print(f"\n🔗 新连接: {client_address}")
                    print(f"⏰ 连接时间: {datetime.now().strftime('%H:%M:%S')}")
                    
                    # 更新连接统计
                    device_name = self._get_device_name(client_address[0])
                    self.device_stats[device_name]['connection_count'] += 1
                    
                    self.client_connections.append(client_socket)
                    
                    # 为每个客户端启动处理线程
                    client_thread = threading.Thread(
                        target=self._handle_raw_grpc_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ 接受连接失败: {e}")
                    
        except Exception as e:
            print(f"❌ 启动收集器失败: {e}")
        
        finally:
            try:
                if self.server_socket:
                    self.server_socket.close()
            except:
                pass
    
    def _handle_raw_grpc_client(self, client_socket, client_address):
        """处理原始 gRPC 客户端连接"""
        
        device_name = self._get_device_name(client_address[0])
        buffer = b""
        
        try:
            # 设置较长的超时时间
            client_socket.settimeout(60.0)
            
            # 发送 HTTP/2 连接前导码响应 (gRPC 基于 HTTP/2)
            print(f"📡 向 {client_address} 发送 HTTP/2 连接前导码...")
            
            # HTTP/2 连接前导码
            preface = b"PRI * HTTP/2.0\r\n\r\nSM\r\n\r\n"
            
            # 等待客户端发送前导码
            initial_data = client_socket.recv(len(preface))
            if initial_data == preface:
                print(f"✅ 收到正确的 HTTP/2 前导码")
                
                # 发送 HTTP/2 SETTINGS 帧
                settings_frame = self._create_http2_settings_frame()
                client_socket.send(settings_frame)
                print(f"📤 发送 HTTP/2 SETTINGS 帧")
                
            else:
                print(f"⚠️ 收到非标准前导码: {initial_data}")
            
            while self.running:
                try:
                    data = client_socket.recv(8192)
                    if not data:
                        print(f"📡 客户端 {client_address} 发送了空数据，连接关闭")
                        break
                    
                    buffer += data
                    print(f"📦 从 {client_address} 接收到 {len(data)} 字节数据")
                    
                    # 处理 HTTP/2 帧
                    while len(buffer) >= 9:  # HTTP/2 帧头最小长度
                        frame_length = int.from_bytes(buffer[0:3], 'big')
                        frame_type = buffer[3]
                        frame_flags = buffer[4]
                        stream_id = int.from_bytes(buffer[5:9], 'big') & 0x7FFFFFFF
                        
                        total_frame_size = 9 + frame_length
                        
                        if len(buffer) < total_frame_size:
                            # 帧不完整，等待更多数据
                            break
                        
                        frame_payload = buffer[9:total_frame_size]
                        buffer = buffer[total_frame_size:]
                        
                        print(f"📋 HTTP/2 帧: 类型={frame_type}, 长度={frame_length}, 流ID={stream_id}")
                        
                        # 处理不同类型的帧
                        self._handle_http2_frame(frame_type, frame_flags, stream_id, frame_payload, client_address)
                
                except socket.timeout:
                    print(f"⏰ 客户端 {client_address} 超时，继续等待...")
                    continue
                except Exception as e:
                    print(f"❌ 处理客户端 {client_address} 数据时出错: {e}")
                    break
        
        except Exception as e:
            print(f"❌ 处理客户端 {client_address} 失败: {e}")
        
        finally:
            try:
                client_socket.close()
                if client_socket in self.client_connections:
                    self.client_connections.remove(client_socket)
                print(f"🔌 客户端 {client_address} 断开连接")
            except:
                pass
    
    def _create_http2_settings_frame(self):
        """创建 HTTP/2 SETTINGS 帧"""
        # SETTINGS 帧格式: 长度(3) + 类型(1) + 标志(1) + 流ID(4) + 设置参数
        settings = b""
        
        # SETTINGS_MAX_CONCURRENT_STREAMS = 100
        settings += struct.pack('>HI', 3, 100)
        
        # SETTINGS_INITIAL_WINDOW_SIZE = 65535
        settings += struct.pack('>HI', 4, 65535)
        
        frame_length = len(settings)
        frame_type = 4  # SETTINGS
        frame_flags = 0
        stream_id = 0
        
        frame = struct.pack('>IHBB', frame_length, frame_type, frame_flags, stream_id) + settings
        return frame
    
    def _handle_http2_frame(self, frame_type, frame_flags, stream_id, payload, client_address):
        """处理 HTTP/2 帧"""
        
        device_name = self._get_device_name(client_address[0])
        
        if frame_type == 0:  # DATA 帧
            print(f"📊 DATA 帧: 流ID={stream_id}, 数据长度={len(payload)}")
            self._process_data_frame(payload, client_address)
            
        elif frame_type == 1:  # HEADERS 帧
            print(f"📋 HEADERS 帧: 流ID={stream_id}")
            
        elif frame_type == 4:  # SETTINGS 帧
            print(f"⚙️ SETTINGS 帧")
            
        elif frame_type == 8:  # WINDOW_UPDATE 帧
            print(f"🪟 WINDOW_UPDATE 帧")
            
        else:
            print(f"❓ 未知帧类型: {frame_type}")
    
    def _process_data_frame(self, data, client_address):
        """处理 DATA 帧中的 telemetry 数据"""
        
        self.total_data_count += 1
        timestamp = datetime.now()
        device_name = self._get_device_name(client_address[0])
        
        # 更新设备统计
        device_stat = self.device_stats[device_name]
        device_stat['message_count'] += 1
        device_stat['last_seen'] = timestamp
        if device_stat['first_seen'] is None:
            device_stat['first_seen'] = timestamp
        
        try:
            # 尝试解析为文本
            text_data = data.decode('utf-8', errors='replace')
            print(f"📝 文本数据: {text_data[:100]}...")
            
            # 保存数据
            self._save_message_data(device_name, {
                'timestamp': timestamp.isoformat(),
                'source_ip': client_address[0],
                'source_port': client_address[1],
                'data_type': 'text',
                'data_length': len(data),
                'data': text_data[:1000]
            })
            
        except:
            # 处理二进制数据
            print(f"🔢 二进制数据: {len(data)} 字节")
            
            self._save_message_data(device_name, {
                'timestamp': timestamp.isoformat(),
                'source_ip': client_address[0],
                'source_port': client_address[1],
                'data_type': 'binary',
                'data_length': len(data),
                'data_hex': data.hex()[:500]
            })
        
        print(f"✅ 处理消息: 设备={device_name}, 长度={len(data)}")
    
    def _get_device_name(self, ip_address):
        """根据IP地址获取设备名称"""
        device_mapping = {
            '***************': 'nokia-alu-1',
            '***************': 'nokia-alu-2', 
            '***************': 'nokia-alu-3',
            '***************': 'nokia-alu-4'
        }
        return device_mapping.get(ip_address, f'unknown-{ip_address}')
    
    def _save_message_data(self, device_name, data):
        """保存消息数据到文件"""
        try:
            date_str = datetime.now().strftime('%Y%m%d')
            filename = f"{self.data_dir}/{device_name}_{date_str}.json"
            
            with open(filename, 'a', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
                f.write('\n')
                
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def _status_reporter(self):
        """状态报告线程"""
        while self.running:
            try:
                time.sleep(30)  # 每30秒报告一次状态
                if self.running:
                    self._print_status()
            except:
                pass
    
    def _print_status(self):
        """打印状态信息"""
        print(f"\n📊 === 状态报告 ({datetime.now().strftime('%H:%M:%S')}) ===")
        print(f"🔗 活跃连接: {len(self.client_connections)}")
        print(f"📦 总消息数: {self.total_data_count}")
        
        active_devices = [name for name, stats in self.device_stats.items() if stats['message_count'] > 0]
        print(f"📱 活跃设备: {len(active_devices)}")
        
        for device in sorted(active_devices):
            stat = self.device_stats[device]
            print(f"   {device}: {stat['message_count']} 消息, {stat['connection_count']} 连接")
    
    def stop(self):
        """停止收集器"""
        print("\n🛑 正在停止收集器...")
        self.running = False
        
        # 关闭所有客户端连接
        for client_socket in self.client_connections[:]:
            try:
                client_socket.close()
            except:
                pass
        
        # 关闭服务器套接字
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        print("✅ 收集器已停止")

def main():
    """主函数"""
    collector = RawGrpcCollector()
    
    try:
        collector.start()
    except KeyboardInterrupt:
        print("\n⚠️ 收到中断信号")
    finally:
        collector.stop()

if __name__ == "__main__":
    main()
