#!/usr/bin/env python3
"""
基础使用示例
演示如何使用 OpenConfig Telemetry Monitor 进行基本的设备监控配置
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.telemetry_config import TelemetryConfigurator
from src.utils import setup_logging


def main():
    """基础使用示例"""
    
    # 设置日志
    setup_logging("INFO")
    
    print("=== OpenConfig Telemetry Monitor 基础使用示例 ===\n")
    
    try:
        # 1. 初始化配置器
        print("1. 初始化 Telemetry 配置器...")
        configurator = TelemetryConfigurator(
            device_config_file="../config/device_config.yaml",
            telemetry_config_file="../config/telemetry_config.yaml"
        )
        print("✓ 配置器初始化成功\n")
        
        # 2. 添加收集器
        print("2. 添加收集器配置...")
        configurator.add_collector(
            name="example-collector",
            address="*************",
            port=57400,
            protocol="grpc",
            encoding="json"
        )
        print("✓ 收集器添加成功\n")
        
        # 3. 列出可用的设备
        print("3. 列出可用的设备...")
        devices = configurator.netconf_client.list_devices()
        if devices:
            print(f"找到 {len(devices)} 个设备:")
            for device in devices:
                print(f"  - {device}")
        else:
            print("未找到配置的设备")
            print("请在 config/device_config.yaml 中添加设备配置")
            return
        print()
        
        # 4. 列出可用的监控模板
        print("4. 列出可用的监控模板...")
        templates = configurator.list_available_templates()
        print(f"可用模板 ({len(templates)} 个):")
        for template in templates:
            template_info = configurator.get_template_info(template)
            print(f"  - {template}: {template_info['description']}")
            print(f"    类别: {', '.join(template_info['categories'])}")
            print(f"    采样间隔: {template_info['sample_interval']}ms")
            print(f"    总指标数: {template_info['total_metrics']}")
        print()
        
        # 5. 选择第一个设备进行配置演示
        device_name = devices[0]
        print(f"5. 使用设备 '{device_name}' 进行配置演示...")
        
        # 测试连接（可选，如果设备不可达会跳过）
        print(f"   测试设备连接...")
        try:
            if configurator.netconf_client.validate_connection(device_name):
                print("   ✓ 设备连接成功")
                
                # 6. 配置基础监控
                print(f"6. 配置基础监控...")
                success = configurator.configure_telemetry(
                    device_name=device_name,
                    template_name="basic",
                    collector_name="example-collector",
                    sample_interval=30000
                )
                
                if success:
                    print("   ✓ 基础监控配置成功")
                else:
                    print("   ✗ 基础监控配置失败")
                
                # 7. 获取配置状态
                print(f"7. 获取配置状态...")
                status = configurator.get_telemetry_status(device_name)
                print(f"   设备: {status['device']}")
                print(f"   配置状态: {'已配置' if status.get('configured') else '未配置'}")
                if status.get('configured'):
                    print(f"   传感器组: {status.get('sensor_groups', 0)}")
                    print(f"   订阅数量: {status.get('subscriptions', 0)}")
                
            else:
                print("   ⚠ 设备连接失败，跳过实际配置")
                print("   演示配置生成...")
                
                # 演示配置生成（不实际应用）
                demo_configuration_generation(configurator)
                
        except Exception as e:
            print(f"   ⚠ 设备连接测试失败: {e}")
            print("   演示配置生成...")
            demo_configuration_generation(configurator)
        
        print("\n8. 清理资源...")
        configurator.cleanup()
        print("✓ 资源清理完成")
        
        print("\n=== 基础使用示例完成 ===")
        
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0


def demo_configuration_generation(configurator):
    """演示配置生成（不实际应用到设备）"""
    
    print("   生成系统监控传感器组...")
    system_sg = configurator.create_sensor_group(
        group_id="demo_system_monitoring",
        category="system",
        metrics=["cpu_utilization", "memory_utilization"],
        description="演示系统监控"
    )
    print(f"   ✓ 传感器组 '{system_sg.group_id}' 创建成功")
    print(f"     包含 {len(system_sg.paths)} 个监控路径")
    
    print("   生成接口监控传感器组...")
    interface_sg = configurator.create_sensor_group(
        group_id="demo_interface_monitoring",
        category="interface",
        metrics=["interface_traffic", "interface_status"],
        description="演示接口监控"
    )
    print(f"   ✓ 传感器组 '{interface_sg.group_id}' 创建成功")
    print(f"     包含 {len(interface_sg.paths)} 个监控路径")
    
    print("   生成订阅配置...")
    subscription = configurator.create_subscription(
        subscription_id="demo_subscription",
        sensor_group=system_sg.group_id,
        destination_group="example-collector",
        sample_interval=30000
    )
    print(f"   ✓ 订阅 '{subscription.subscription_id}' 创建成功")
    
    print("   配置生成演示完成")


if __name__ == "__main__":
    sys.exit(main())