#!/usr/bin/env python3
"""
OpenConfig Telemetry Monitor
基于 ncclient 和 OpenConfig YANG 模型的网络设备 Telemetry 配置工具
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="openconfig-telemetry-monitor",
    version="1.0.0",
    author="Telemetry Monitor Team",
    author_email="<EMAIL>",
    description="基于 OpenConfig YANG 模型的网络设备 Telemetry 配置工具",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/openconfig-telemetry-monitor",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: System Administrators",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: System :: Networking :: Monitoring",
        "Topic :: System :: Systems Administration",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "telemetry-monitor=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json"],
    },
)