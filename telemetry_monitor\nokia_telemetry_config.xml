
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <sensor-groups>
      
      <sensor-group>
        <sensor-group-id>basic_system_basic</sensor-group-id>
        <config>
          <sensor-group-id>basic_system_basic</sensor-group-id>
        </config>
        <sensor-paths>
          
          <sensor-path>
            <path>/openconfig-system:system/cpus/cpu/state/total</path>
            <config>
              <path>/openconfig-system:system/cpus/cpu/state/total</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-system:system/cpus/cpu/state/user</path>
            <config>
              <path>/openconfig-system:system/cpus/cpu/state/user</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-system:system/cpus/cpu/state/kernel</path>
            <config>
              <path>/openconfig-system:system/cpus/cpu/state/kernel</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-system:system/cpus/cpu/state/idle</path>
            <config>
              <path>/openconfig-system:system/cpus/cpu/state/idle</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-system:system/memory/state/physical</path>
            <config>
              <path>/openconfig-system:system/memory/state/physical</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-system:system/memory/state/reserved</path>
            <config>
              <path>/openconfig-system:system/memory/state/reserved</path>
            </config>
          </sensor-path>
            
        </sensor-paths>
      </sensor-group>
            

      <sensor-group>
        <sensor-group-id>basic_interface_basic</sensor-group-id>
        <config>
          <sensor-group-id>basic_interface_basic</sensor-group-id>
        </config>
        <sensor-paths>
          
          <sensor-path>
            <path>/openconfig-interfaces:interfaces/interface/state/counters/in-octets</path>
            <config>
              <path>/openconfig-interfaces:interfaces/interface/state/counters/in-octets</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-interfaces:interfaces/interface/state/counters/out-octets</path>
            <config>
              <path>/openconfig-interfaces:interfaces/interface/state/counters/out-octets</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-interfaces:interfaces/interface/state/counters/in-pkts</path>
            <config>
              <path>/openconfig-interfaces:interfaces/interface/state/counters/in-pkts</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-interfaces:interfaces/interface/state/counters/out-pkts</path>
            <config>
              <path>/openconfig-interfaces:interfaces/interface/state/counters/out-pkts</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-interfaces:interfaces/interface/state/admin-status</path>
            <config>
              <path>/openconfig-interfaces:interfaces/interface/state/admin-status</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-interfaces:interfaces/interface/state/oper-status</path>
            <config>
              <path>/openconfig-interfaces:interfaces/interface/state/oper-status</path>
            </config>
          </sensor-path>
            

          <sensor-path>
            <path>/openconfig-interfaces:interfaces/interface/state/last-change</path>
            <config>
              <path>/openconfig-interfaces:interfaces/interface/state/last-change</path>
            </config>
          </sensor-path>
            
        </sensor-paths>
      </sensor-group>
            
    </sensor-groups>
    <destination-groups>
      
      <destination-group>
        <group-id>nokia-test-collector</group-id>
        <config>
          <group-id>nokia-test-collector</group-id>
        </config>
        <destinations>
          
          <destination>
            <destination-address>*************</destination-address>
            <destination-port>57400</destination-port>
            <config>
              <destination-address>*************</destination-address>
              <destination-port>57400</destination-port>
              <destination-protocol>grpc</destination-protocol>
              <encoding>json</encoding>
            </config>
          </destination>
            
        </destinations>
      </destination-group>
            
    </destination-groups>
    <subscriptions>
      
      <subscription>
        <subscription-id>sub_basic_system_basic</subscription-id>
        <config>
          <subscription-id>sub_basic_system_basic</subscription-id>
          <local-source-address></local-source-address>
          <originated-qos-marking>0</originated-qos-marking>
        </config>
        <sensor-profiles>
          <sensor-profile>
            <sensor-group>basic_system_basic</sensor-group>
            <config>
              <sensor-group>basic_system_basic</sensor-group>
              <sample-interval>30000</sample-interval>
              <suppress-redundant>false</suppress-redundant>
              <heartbeat-interval>60000</heartbeat-interval>
            </config>
          </sensor-profile>
        </sensor-profiles>
        <destination-groups>
          <destination-group>nokia-test-collector</destination-group>
        </destination-groups>
      </subscription>
            

      <subscription>
        <subscription-id>sub_basic_interface_basic</subscription-id>
        <config>
          <subscription-id>sub_basic_interface_basic</subscription-id>
          <local-source-address></local-source-address>
          <originated-qos-marking>0</originated-qos-marking>
        </config>
        <sensor-profiles>
          <sensor-profile>
            <sensor-group>basic_interface_basic</sensor-group>
            <config>
              <sensor-group>basic_interface_basic</sensor-group>
              <sample-interval>30000</sample-interval>
              <suppress-redundant>false</suppress-redundant>
              <heartbeat-interval>60000</heartbeat-interval>
            </config>
          </sensor-profile>
        </sensor-profiles>
        <destination-groups>
          <destination-group>nokia-test-collector</destination-group>
        </destination-groups>
      </subscription>
            
    </subscriptions>
  </telemetry-system>
</config>
            