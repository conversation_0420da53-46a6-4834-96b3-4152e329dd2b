<rpc-reply message-id="urn:uuid:9e5fe1a8-7f06-4745-a0ba-22dd39f5aa2b">
  <data>
    <interfaces>
      <interface>
        <name>INTERFACE-1-1-C3</name>
        <config>
          <description>INTERFACE-1-1-C3</description>
          <name>INTERFACE-1-1-C3</name>
        </config>
        <ethernet>
          <config>
            <fault-delay-enabled>false</fault-delay-enabled>
            <client-fec>DISABLED</client-fec>
            <client-als>ETHERNET</client-als>
            <port-speed>SPEED_100GB</port-speed>
          </config>
        </ethernet>
      </interface>
      <interface>
        <name>INTERFACE-1-12-CIT</name>
        <ethernet>
          <config>
            <duplex-mode>HALF</duplex-mode>
            <mac-address>A4:E3:1B:F0:43:04</mac-address>
            <port-speed>SPEED_10MB</port-speed>
          </config>
        </ethernet>
        <config>
          <description>INTERFACE-1-12-CIT</description>
          <name>INTERFACE-1-12-CIT</name>
          <enabled>true</enabled>
        </config>
        <subinterfaces>
          <subinterface>
            <index>1</index>
            <config>
              <index>1</index>
            </config>
            <ipv4>
              <addresses>
                <address>
                  <ip>**********</ip>
                  <config>
                    <ip>**********</ip>
                    <gateway>0.0.0.0</gateway>
                    <prefix-length>24</prefix-length>
                  </config>
                </address>
              </addresses>
            </ipv4>
          </subinterface>
        </subinterfaces>
      </interface>
      <interface>
        <name>INTERFACE-1-9-LOOPBACK</name>
        <config>
          <description>INTERFACE-1-9-LOOPBACK</description>
          <name>INTERFACE-1-9-LOOPBACK</name>
          <enabled>true</enabled>
        </config>
        <subinterfaces>
          <subinterface>
            <index>1</index>
            <ipv4>
              <addresses>
                <address>
                  <ip>**********</ip>
                  <config>
                    <ip>**********</ip>
                    <gateway>0.0.0.0</gateway>
                    <prefix-length>32</prefix-length>
                  </config>
                </address>
              </addresses>
            </ipv4>
            <config>
              <index>1</index>
            </config>
            <ipv6>
              <addresses>
                <address>
                  <ip>fd00:10::123</ip>
                  <config>
                    <ip>fd00:10::123</ip>
                    <prefix-length>128</prefix-length>
                  </config>
                </address>
              </addresses>
            </ipv6>
          </subinterface>
        </subinterfaces>
      </interface>
      <interface>
        <name>INTERFACE-1-9-NM2</name>
        <ethernet>
          <config>
            <duplex-mode>HALF</duplex-mode>
            <mac-address>7C:41:A2:F2:79:8B</mac-address>
            <port-speed>SPEED_10MB</port-speed>
          </config>
        </ethernet>
        <config>
          <description>INTERFACE-1-9-NM2</description>
          <name>INTERFACE-1-9-NM2</name>
          <enabled>true</enabled>
        </config>
        <subinterfaces>
          <subinterface>
            <index>1</index>
            <ipv4>
              <addresses>
                <address>
                  <ip>***********</ip>
                  <config>
                    <ip>***********</ip>
                    <gateway>0.0.0.0</gateway>
                    <prefix-length>24</prefix-length>
                  </config>
                </address>
              </addresses>
              <config>
                <dhcp-client>false</dhcp-client>
              </config>
            </ipv4>
            <config>
              <index>1</index>
            </config>
            <ipv6>
              <config>
                <dhcp-client>false</dhcp-client>
              </config>
            </ipv6>
          </subinterface>
        </subinterfaces>
      </interface>
      <interface>
        <name>INTERFACE-1-9-NM1</name>
        <ethernet>
          <config>
            <duplex-mode>FULL</duplex-mode>
            <mac-address>7C:41:A2:F2:79:8A</mac-address>
            <port-speed>SPEED_1GB</port-speed>
          </config>
        </ethernet>
        <config>
          <description>INTERFACE-1-9-NM1</description>
          <name>INTERFACE-1-9-NM1</name>
          <enabled>true</enabled>
        </config>
        <subinterfaces>
          <subinterface>
            <index>1</index>
            <ipv4>
              <addresses>
                <address>
                  <ip>***************</ip>
                  <config>
                    <ip>***************</ip>
                    <gateway>0.0.0.0</gateway>
                    <prefix-length>24</prefix-length>
                  </config>
                </address>
              </addresses>
              <config>
                <dhcp-client>false</dhcp-client>
              </config>
            </ipv4>
            <config>
              <index>1</index>
            </config>
            <ipv6>
              <addresses>
                <address>
                  <ip>fd00:100:120:162::123</ip>
                  <config>
                    <ip>fd00:100:120:162::123</ip>
                    <prefix-length>80</prefix-length>
                  </config>
                </address>
              </addresses>
              <config>
                <dhcp-client>false</dhcp-client>
              </config>
            </ipv6>
          </subinterface>
        </subinterfaces>
      </interface>
      <interface>
        <name>INTERFACE-1-11-CIT</name>
        <ethernet>
          <config>
            <mac-address>A4:E3:1B:F0:43:10</mac-address>
            <port-speed>SPEED_UNKNOWN</port-speed>
          </config>
        </ethernet>
        <config>
          <description>INTERFACE-1-11-CIT</description>
          <name>INTERFACE-1-11-CIT</name>
          <enabled>true</enabled>
        </config>
        <subinterfaces>
          <subinterface>
            <index>1</index>
            <config>
              <index>1</index>
            </config>
            <ipv4>
              <addresses>
                <address>
                  <ip>**********</ip>
                  <config>
                    <ip>**********</ip>
                    <gateway>0.0.0.0</gateway>
                    <prefix-length>24</prefix-length>
                  </config>
                </address>
              </addresses>
            </ipv4>
          </subinterface>
        </subinterfaces>
      </interface>
    </interfaces>
    <lldp>
      <config>
        <enabled>true</enabled>
        <system-name>Nokia</system-name>
      </config>
      <interfaces>
        <interface>
          <name>INTERFACE-1-1-C4</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-1-C4</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-1-C3</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-1-C3</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-1-C2</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-1-C2</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-1-C1</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-1-C1</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-8-C2</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-8-C2</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-8-C3</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-8-C3</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-7-C2</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-7-C2</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-8-C1</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-8-C1</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-7-C1</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-7-C1</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-5-C1</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-5-C1</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-7-C4</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-7-C4</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-7-C3</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-7-C3</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-5-C2</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-5-C2</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-4-C1</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-4-C1</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-8-C4</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-8-C4</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-4-C2</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-4-C2</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-5-C3</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-5-C3</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-4-C3</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-4-C3</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-5-C4</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-5-C4</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-4-C4</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-4-C4</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-1-C6</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-1-C6</name>
          </config>
        </interface>
        <interface>
          <name>INTERFACE-1-1-C5</name>
          <config>
            <enabled>false</enabled>
            <name>INTERFACE-1-1-C5</name>
          </config>
        </interface>
      </interfaces>
    </lldp>
    <terminal-device>
      <operational-modes>
        <mode>
          <mode-id>320</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>0</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>63</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>383</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>4</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>5</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>66</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>520</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>71</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>263</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>204</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>205</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>271</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>405</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>83</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>29</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>605</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>283</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>33</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>229</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>227</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>40</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>1000</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>240</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>305</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>302</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>366</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>371</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>505</mode-id>
          <config/>
        </mode>
        <mode>
          <mode-id>502</mode-id>
          <config/>
        </mode>
      </operational-modes>
      <logical-channels>
        <channel>
          <index>272662845</index>
          <otn>
            <config>
              <tti-msg-expected/>
              <tti-msg-auto>false</tti-msg-auto>
              <tti-msg-transmit/>
            </config>
          </otn>
          <logical-channel-assignments>
            <assignment>
              <index>1</index>
              <config>
                <tributary-slot-index>1</tributary-slot-index>
                <mapping>x:GMP</mapping>
                <assignment-type>LOGICAL_CHANNEL</assignment-type>
                <allocation>100</allocation>
                <description>ODUC3-1-1-L1</description>
                <index>1</index>
                <logical-channel>272662591</logical-channel>
              </config>
            </assignment>
          </logical-channel-assignments>
          <config>
            <logical-channel-type>x:PROT_OTN</logical-channel-type>
            <description>ODU4-1-1-L1-1</description>
            <loopback-mode>NONE</loopback-mode>
            <index>272662845</index>
            <rate-class>x:TRIB_RATE_100G</rate-class>
            <trib-protocol>x:PROT_ODU4</trib-protocol>
            <admin-state>ENABLED</admin-state>
          </config>
        </channel>
        <channel>
          <index>272663101</index>
          <otn>
            <config>
              <tti-msg-expected/>
              <tti-msg-auto>false</tti-msg-auto>
              <tti-msg-transmit/>
            </config>
          </otn>
          <logical-channel-assignments>
            <assignment>
              <index>1</index>
              <config>
                <tributary-slot-index>1</tributary-slot-index>
                <mapping>x:GMP</mapping>
                <assignment-type>LOGICAL_CHANNEL</assignment-type>
                <allocation>100</allocation>
                <description>ODUC3-1-1-L1</description>
                <index>1</index>
                <logical-channel>272662591</logical-channel>
              </config>
            </assignment>
          </logical-channel-assignments>
          <config>
            <logical-channel-type>x:PROT_OTN</logical-channel-type>
            <description>ODU4-1-1-L1-2</description>
            <loopback-mode>NONE</loopback-mode>
            <index>272663101</index>
            <rate-class>x:TRIB_RATE_100G</rate-class>
            <trib-protocol>x:PROT_ODU4</trib-protocol>
            <admin-state>ENABLED</admin-state>
          </config>
        </channel>
        <channel>
          <index>272663357</index>
          <otn>
            <config>
              <tti-msg-expected/>
              <tti-msg-auto>false</tti-msg-auto>
              <tti-msg-transmit/>
            </config>
          </otn>
          <logical-channel-assignments>
            <assignment>
              <index>1</index>
              <config>
                <tributary-slot-index>1</tributary-slot-index>
                <mapping>x:GMP</mapping>
                <assignment-type>LOGICAL_CHANNEL</assignment-type>
                <allocation>100</allocation>
                <description>ODUC3-1-1-L1</description>
                <index>1</index>
                <logical-channel>272662591</logical-channel>
              </config>
            </assignment>
          </logical-channel-assignments>
          <config>
            <logical-channel-type>x:PROT_OTN</logical-channel-type>
            <description>ODU4-1-1-L1-3</description>
            <loopback-mode>NONE</loopback-mode>
            <index>272663357</index>
            <rate-class>x:TRIB_RATE_100G</rate-class>
            <trib-protocol>x:PROT_ODU4</trib-protocol>
            <admin-state>ENABLED</admin-state>
          </config>
        </channel>
        <channel>
          <index>272662576</index>
          <otn>
            <config>
              <tti-msg-expected>s-otu-b-1-5-L1d-otu-b-1-5-L1</tti-msg-expected>
              <tti-msg-auto>false</tti-msg-auto>
              <tti-msg-transmit>s-otu-a-1-1-L1d-otu-a-1-1-L1</tti-msg-transmit>
            </config>
          </otn>
          <logical-channel-assignments>
            <assignment>
              <index>1</index>
              <config>
                <tributary-slot-index>1</tributary-slot-index>
                <mapping>x:BMP</mapping>
                <assignment-type>OPTICAL_CHANNEL</assignment-type>
                <allocation>300</allocation>
                <description>OCH-1-1-L1</description>
                <index>1</index>
                <optical-channel>OCH-1-1-L1</optical-channel>
              </config>
            </assignment>
          </logical-channel-assignments>
          <config>
            <logical-channel-type>x:PROT_OTN</logical-channel-type>
            <description>OTUC3-1-1-L1</description>
            <loopback-mode>NONE</loopback-mode>
            <index>272662576</index>
            <rate-class>x:TRIB_RATE_300G</rate-class>
            <trib-protocol>x:PROT_OTUCN</trib-protocol>
            <admin-state>ENABLED</admin-state>
          </config>
        </channel>
        <channel>
          <index>272760893</index>
          <otn>
            <config>
              <tti-msg-expected/>
              <tti-msg-auto>false</tti-msg-auto>
              <delay-measurement-mode>TRANSPARENT</delay-measurement-mode>
              <tti-msg-transmit/>
            </config>
          </otn>
          <logical-channel-assignments>
            <assignment>
              <index>1</index>
              <config>
                <tributary-slot-index>1</tributary-slot-index>
                <mapping>x:GMP</mapping>
                <assignment-type>LOGICAL_CHANNEL</assignment-type>
                <allocation>100</allocation>
                <description>ODU4-1-1-L1-1</description>
                <index>1</index>
                <logical-channel>272662845</logical-channel>
              </config>
            </assignment>
          </logical-channel-assignments>
          <config>
            <logical-channel-type>x:PROT_OTN</logical-channel-type>
            <description>ODU4-1-1-C3</description>
            <loopback-mode>NONE</loopback-mode>
            <index>272760893</index>
            <rate-class>x:TRIB_RATE_100G</rate-class>
            <trib-protocol>x:PROT_ODU4</trib-protocol>
            <admin-state>ENABLED</admin-state>
          </config>
        </channel>
        <channel>
          <index>272662591</index>
          <otn>
            <config>
              <tti-msg-expected>s-odu-b-1-5-L1d-odu-a-1-5-L1</tti-msg-expected>
              <tti-msg-auto>false</tti-msg-auto>
              <delay-measurement-mode>TRANSPARENT</delay-measurement-mode>
              <tti-msg-transmit>s-odu-a-1-1-L1d-odu-a-1-1-L1</tti-msg-transmit>
            </config>
          </otn>
          <logical-channel-assignments>
            <assignment>
              <index>1</index>
              <config>
                <tributary-slot-index>1</tributary-slot-index>
                <mapping>x:BMP</mapping>
                <assignment-type>LOGICAL_CHANNEL</assignment-type>
                <allocation>300</allocation>
                <description>OTUC3-1-1-L1</description>
                <index>1</index>
                <logical-channel>272662576</logical-channel>
              </config>
            </assignment>
          </logical-channel-assignments>
          <config>
            <logical-channel-type>x:PROT_OTN</logical-channel-type>
            <description>ODUC3-1-1-L1</description>
            <loopback-mode>NONE</loopback-mode>
            <index>272662591</index>
            <rate-class>x:TRIB_RATE_300G</rate-class>
            <trib-protocol>x:PROT_ODUCN</trib-protocol>
            <admin-state>ENABLED</admin-state>
          </config>
        </channel>
        <channel>
          <index>272760868</index>
          <ethernet>
            <config/>
          </ethernet>
          <ingress>
            <config>
              <transceiver>TRANSCEIVER-1-1-C3</transceiver>
            </config>
          </ingress>
          <logical-channel-assignments>
            <assignment>
              <index>1</index>
              <config>
                <tributary-slot-index>1</tributary-slot-index>
                <mapping>x:GMP</mapping>
                <assignment-type>LOGICAL_CHANNEL</assignment-type>
                <allocation>100</allocation>
                <description>ODU4-1-1-C3</description>
                <index>1</index>
                <logical-channel>272760893</logical-channel>
              </config>
            </assignment>
          </logical-channel-assignments>
          <config>
            <logical-channel-type>x:PROT_ETHERNET</logical-channel-type>
            <description>100GE-1-1-C3</description>
            <loopback-mode>NONE</loopback-mode>
            <index>272760868</index>
            <rate-class>x:TRIB_RATE_100G</rate-class>
            <trib-protocol>x:PROT_100GE</trib-protocol>
            <admin-state>ENABLED</admin-state>
          </config>
        </channel>
      </logical-channels>
    </terminal-device>
    <system>
      <alarms>
        <alarm>
          <id>3741708</id>
          <state>
            <resource>TRANSCEIVER-1-1-C3</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275953000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741708</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -20.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717041</id>
          <state>
            <resource>PORT-1-3-1-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717041</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.09 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717043</id>
          <state>
            <resource>APS-1-3-1</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717043</id>
            <alarm-abbreviate>OLP_PRIMA_INVALID</alarm-abbreviate>
            <ip>***************</ip>
            <text>Primary Path Invalid</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741701</id>
          <state>
            <resource>OTUC3-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275951000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741701</id>
            <alarm-abbreviate>TCA_FEC_PRE_H_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: 2.40E-02 , Current Register Value: 1.00E+00 </text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741700</id>
          <state>
            <resource>OTUC3-1-1-L1</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754275952000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741700</id>
            <alarm-abbreviate>OTU-SF</alarm-abbreviate>
            <ip>***************</ip>
            <text>OTUk Sever Signal Failure</text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741703</id>
          <state>
            <resource>ODUC3-1-1-L1</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754275955000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741703</id>
            <alarm-abbreviate>ODUK_PM_SSF</alarm-abbreviate>
            <ip>***************</ip>
            <text>ODUk Sever Signal Failure</text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741702</id>
          <state>
            <resource>OTUC3-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275951000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741702</id>
            <alarm-abbreviate>TCA_FEC_PRE_H_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: 2.40E-02 , Current Register Value: 1.00E+00 </text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717049</id>
          <state>
            <resource>APS-1-3-2</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717049</id>
            <alarm-abbreviate>OLP_PRIMA_INVALID</alarm-abbreviate>
            <ip>***************</ip>
            <text>Primary Path Invalid</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717048</id>
          <state>
            <resource>PORT-1-3-1-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717048</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.47 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741710</id>
          <state>
            <resource>TRANSCEIVER-1-1-C3</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275953000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741710</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -20.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717045</id>
          <state>
            <resource>PORT-1-3-1-APSP</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717045</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717044</id>
          <state>
            <resource>APS-1-3-1</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717044</id>
            <alarm-abbreviate>OLP_SECON_INVALID</alarm-abbreviate>
            <ip>***************</ip>
            <text>Secondary Path Invalid</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717047</id>
          <state>
            <resource>PORT-1-3-1-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717047</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.47 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3733012</id>
          <state>
            <resource>PORT-1-3-2-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754265601000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3733012</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.10 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3733016</id>
          <state>
            <resource>PORT-1-3-3-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754265601000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3733016</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -42.37 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3763184</id>
          <state>
            <resource>PORT-1-3-3-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754315101000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3763184</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.28 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3765484</id>
          <state>
            <resource>PORT-1-3-2-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754320501000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3765484</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.77 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3733018</id>
          <state>
            <resource>PORT-1-3-3-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754265601000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3733018</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.44 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741716</id>
          <state>
            <resource>TRANSCEIVER-1-1-C3-1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275953000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741716</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -11.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741718</id>
          <state>
            <resource>TRANSCEIVER-1-1-C3-2</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275953000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741718</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -11.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741712</id>
          <state>
            <resource>ODU4-1-1-C3</resource>
            <service-affecting>true</service-affecting>
            <severity>x:MAJOR</severity>
            <time-created>1754275955000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741712</id>
            <alarm-abbreviate>ODUK_PM_AIS</alarm-abbreviate>
            <ip>***************</ip>
            <text>ODUk AIS</text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3718084</id>
          <state>
            <resource>PORT-1-3-4-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754247601000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3718084</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.67 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741714</id>
          <state>
            <resource>TRANSCEIVER-1-1-C3-1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275953000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741714</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -11.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3763571</id>
          <state>
            <resource>PORT-1-3-2-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754316001000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3763571</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -42.92 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717039</id>
          <state>
            <resource>PORT-1-3-1-APSC</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717039</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741720</id>
          <state>
            <resource>TRANSCEIVER-1-1-C3-2</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275953000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741720</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -11.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3733022</id>
          <state>
            <resource>PORT-1-3-4-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754265601000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3733022</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.44 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3733024</id>
          <state>
            <resource>PORT-1-3-4-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754265601000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3733024</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.09 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717063</id>
          <state>
            <resource>PORT-1-3-2-APSP</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717063</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717062</id>
          <state>
            <resource>APS-1-3-4</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717062</id>
            <alarm-abbreviate>OLP_SECON_INVALID</alarm-abbreviate>
            <ip>***************</ip>
            <text>Secondary Path Invalid</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717065</id>
          <state>
            <resource>PORT-1-3-2-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717065</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.09 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717061</id>
          <state>
            <resource>APS-1-3-4</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717061</id>
            <alarm-abbreviate>OLP_PRIMA_INVALID</alarm-abbreviate>
            <ip>***************</ip>
            <text>Primary Path Invalid</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741768</id>
          <state>
            <resource>ODU4-1-1-C3</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275963000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741768</id>
            <alarm-abbreviate>TCA_UAS_H_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: 10, Current Register Value: 10</text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717060</id>
          <state>
            <resource>PORT-1-3-2-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717060</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -42.92 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3765103</id>
          <state>
            <resource>PORT-1-3-4-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754319601000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3765103</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.09 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717067</id>
          <state>
            <resource>PORT-1-3-2-APSS</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717067</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3714196</id>
          <state>
            <resource>PORT-1-3-2-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754238556000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3714196</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -42.92 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717069</id>
          <state>
            <resource>PORT-1-3-2-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717069</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.32 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3714637</id>
          <state>
            <resource>PORT-1-3-3-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754239601000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3714637</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.69 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3714635</id>
          <state>
            <resource>PORT-1-3-3-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754239601000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3714635</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.98 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3714181</id>
          <state>
            <resource>PORT-1-3-1-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754238556000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3714181</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.37 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717051</id>
          <state>
            <resource>PORT-1-3-1-APSS</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717051</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717054</id>
          <state>
            <resource>PORT-1-3-1-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717054</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.87 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717053</id>
          <state>
            <resource>PORT-1-3-1-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717053</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.87 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3714183</id>
          <state>
            <resource>PORT-1-3-1-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754238556000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3714183</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -42.84 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3763948</id>
          <state>
            <resource>PORT-1-3-1-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754316901000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3763948</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.01 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717050</id>
          <state>
            <resource>APS-1-3-2</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717050</id>
            <alarm-abbreviate>OLP_SECON_INVALID</alarm-abbreviate>
            <ip>***************</ip>
            <text>Secondary Path Invalid</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741785</id>
          <state>
            <resource>OCH-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754276013000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741785</id>
            <alarm-abbreviate>TCA_OSNR_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: 14.00 dB, Current Register Value: 8.59 dB</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3714189</id>
          <state>
            <resource>PORT-1-3-2-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754238556000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3714189</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.77 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717059</id>
          <state>
            <resource>PORT-1-3-2-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717059</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -42.92 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741787</id>
          <state>
            <resource>OCH-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754276013000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741787</id>
            <alarm-abbreviate>TCA_Q_VALUE_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: 6.20 dB, Current Register Value: 0.00 dB</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741786</id>
          <state>
            <resource>OCH-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754276013000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741786</id>
            <alarm-abbreviate>TCA_Q_VALUE_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: 6.20 dB, Current Register Value: 0.00 dB</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717056</id>
          <state>
            <resource>APS-1-3-3</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717056</id>
            <alarm-abbreviate>OLP_SECON_INVALID</alarm-abbreviate>
            <ip>***************</ip>
            <text>Secondary Path Invalid</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717055</id>
          <state>
            <resource>APS-1-3-3</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717055</id>
            <alarm-abbreviate>OLP_PRIMA_INVALID</alarm-abbreviate>
            <ip>***************</ip>
            <text>Primary Path Invalid</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3714187</id>
          <state>
            <resource>PORT-1-3-1-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754238556000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3714187</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.01 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717057</id>
          <state>
            <resource>PORT-1-3-2-APSC</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717057</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3713778</id>
          <state>
            <resource>INTERFACE-1-9-NM2</resource>
            <service-affecting>false</service-affecting>
            <severity>x:MINOR</severity>
            <time-created>1754237378000000000</time-created>
            <vendor-type>S2UP</vendor-type>
            <id>3713778</id>
            <alarm-abbreviate>LINK_DOWN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Data Link Down</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3766260</id>
          <state>
            <resource>PORT-1-3-4-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754322301000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3766260</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.37 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3766264</id>
          <state>
            <resource>ODU4-1-1-C3</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754322310000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3766264</id>
            <alarm-abbreviate>TCA_UAS_H_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: 10, Current Register Value: 10</text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3766263</id>
          <state>
            <resource>OTUC3-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754322310000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3766263</id>
            <alarm-abbreviate>TCA_UAS_H_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: 10, Current Register Value: 10</text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3766262</id>
          <state>
            <resource>PORT-1-3-4-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754322301000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3766262</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -42.37 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717087</id>
          <state>
            <resource>PORT-1-3-4-APSP</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717087</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717086</id>
          <state>
            <resource>PORT-1-3-4-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717086</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.10 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717080</id>
          <state>
            <resource>PORT-1-3-3-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246492000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717080</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.69 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717083</id>
          <state>
            <resource>PORT-1-3-4-APSC</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246494000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717083</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3766258</id>
          <state>
            <resource>PORT-1-3-1-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754322301000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3766258</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -42.92 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3766256</id>
          <state>
            <resource>PORT-1-3-1-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754322301000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3766256</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.47 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717089</id>
          <state>
            <resource>PORT-1-3-4-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717089</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.19 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717088</id>
          <state>
            <resource>PORT-1-3-4-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717088</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.19 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3755523</id>
          <state>
            <resource>PORT-1-3-4-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754297101000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3755523</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.32 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3755125</id>
          <state>
            <resource>PORT-1-3-1-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754296201000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3755125</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.09 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717073</id>
          <state>
            <resource>PORT-1-3-3-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717073</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.77 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717075</id>
          <state>
            <resource>PORT-1-3-3-APSP</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246494000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717075</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717072</id>
          <state>
            <resource>PORT-1-3-3-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717072</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.77 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3740547</id>
          <state>
            <resource>PORT-1-3-3-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754274601000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3740547</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.44 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717071</id>
          <state>
            <resource>PORT-1-3-3-APSC</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717071</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717078</id>
          <state>
            <resource>PORT-1-3-3-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717078</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.28 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717079</id>
          <state>
            <resource>PORT-1-3-3-APSS</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246494000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717079</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3714646</id>
          <state>
            <resource>PORT-1-3-4-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754239600000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3714646</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -42.22 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3762789</id>
          <state>
            <resource>PORT-1-3-2-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754314201000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3762789</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.09 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741727</id>
          <state>
            <resource>TRANSCEIVER-1-1-C3-4</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275953000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741727</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -11.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3764329</id>
          <state>
            <resource>PORT-1-3-3-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754317801000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3764329</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.98 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741729</id>
          <state>
            <resource>TRANSCEIVER-1-1-C3-4</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275953000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741729</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -11.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741723</id>
          <state>
            <resource>TRANSCEIVER-1-1-C3-3</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275953000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741723</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -11.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741725</id>
          <state>
            <resource>TRANSCEIVER-1-1-C3-3</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275953000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741725</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -11.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741697</id>
          <state>
            <resource>TRANSCEIVER-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275951000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741697</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -20.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741730</id>
          <state>
            <resource>ODU4-1-1-C3</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754275958000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741730</id>
            <alarm-abbreviate>ODUK_PM_SSF</alarm-abbreviate>
            <ip>***************</ip>
            <text>ODUk Sever Signal Failure</text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741699</id>
          <state>
            <resource>TRANSCEIVER-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275951000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741699</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -20.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741692</id>
          <state>
            <resource>OCH-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275951000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741692</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -20.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741694</id>
          <state>
            <resource>OCH-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275951000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741694</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -20.00 dBm, Current Register Value: -40.00 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741690</id>
          <state>
            <resource>PORT-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:MINOR</severity>
            <time-created>1754275950000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741690</id>
            <alarm-abbreviate>LINK_DOWN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Data Link Down</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3741739</id>
          <state>
            <resource>OTUC3-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754275959000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3741739</id>
            <alarm-abbreviate>TCA_UAS_H_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: 10, Current Register Value: 10</text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3764718</id>
          <state>
            <resource>PORT-1-3-2-APSP</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754318701000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3764718</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.10 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717091</id>
          <state>
            <resource>PORT-1-3-4-APSS</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717091</id>
            <alarm-abbreviate>RX_LOS</alarm-abbreviate>
            <ip>***************</ip>
            <text>Rx LOS</text>
            <type-id>LINE-LOS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3742789</id>
          <state>
            <resource>OTUC3-1-1-L1</resource>
            <service-affecting>true</service-affecting>
            <severity>x:CRITICAL</severity>
            <time-created>1754277322000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3742789</id>
            <alarm-abbreviate>LOF</alarm-abbreviate>
            <ip>***************</ip>
            <text>OTUk LOF</text>
            <type-id>OTS</type-id>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3717093</id>
          <state>
            <resource>PORT-1-3-4-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754246493000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3717093</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_24H</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -43.37 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3764722</id>
          <state>
            <resource>PORT-1-3-3-APSC</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754318701000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3764722</id>
            <alarm-abbreviate>TCA_OPT_INPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -42.29 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3764720</id>
          <state>
            <resource>PORT-1-3-2-APSS</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754318701000000000</time-created>
            <vendor-type>OLP4</vendor-type>
            <id>3764720</id>
            <alarm-abbreviate>TCA_OPT_OUTPUT_POWER_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: -35.00 dBm, Current Register Value: -44.32 dBm</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3742790</id>
          <state>
            <resource>TRANSCEIVER-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:MINOR</severity>
            <time-created>1754277329000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3742790</id>
            <alarm-abbreviate>EXTIFFAIL</alarm-abbreviate>
            <ip>***************</ip>
            <text>External Interface Failure</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
        <alarm>
          <id>3742151</id>
          <state>
            <resource>OCH-1-1-L1</resource>
            <service-affecting>false</service-affecting>
            <severity>x:WARNING</severity>
            <time-created>1754276401000000000</time-created>
            <vendor-type>S6T600D</vendor-type>
            <id>3742151</id>
            <alarm-abbreviate>TCA_OSNR_L_15MIN</alarm-abbreviate>
            <ip>***************</ip>
            <text>Current Threshold Value: 14.00 dB, Current Register Value: 0.00 dB</text>
            <hostname>Nokia</hostname>
          </state>
        </alarm>
      </alarms>
      <software>
        <state>
          <images>
            <image>
              <image-name>1830PSIS_CD-58.01-40-02</image-name>
              <image-status>NORMAL</image-status>
            </image>
            <active-image>1830PSIS_CD-58.01-40-02</active-image>
            <max-image-num>2</max-image-num>
          </images>
          <databases>
            <database>
              <db-name/>
              <db-status>NORMAL</db-status>
            </database>
            <database>
              <db-name>jwang089_db_MixRate_100GBE_NEA</db-name>
              <db-status>NORMAL</db-status>
            </database>
            <database>
              <db-name>jwang089_mix_rate_400gbe_db_test_NEA</db-name>
              <db-status>NORMAL</db-status>
            </database>
            <active-db/>
            <max-db-num>5</max-db-num>
          </databases>
        </state>
      </software>
      <config>
        <domain-name>Default</domain-name>
        <ospf-enabled>false</ospf-enabled>
        <hostname>Nokia</hostname>
      </config>
      <clock>
        <config>
          <timezone-name>Asia/Shanghai</timezone-name>
        </config>
      </clock>
      <ntp>
        <servers>
          <server>
            <address>**************</address>
            <config>
              <address>**************</address>
              <port>123</port>
              <iburst>false</iburst>
              <association-type>SERVER</association-type>
              <version>4</version>
              <prefer>false</prefer>
            </config>
          </server>
        </servers>
        <config>
          <ntp-source-address>**************</ntp-source-address>
          <enabled>true</enabled>
        </config>
      </ntp>
      <aaa>
        <authentication>
          <users>
            <user>
              <username>admin</username>
              <config>
                <username>admin</username>
                <role>SYSTEM_ROLE_ADMIN</role>
                <password>******</password>
              </config>
            </user>
          </users>
        </authentication>
      </aaa>
    </system>
    <components>
      <component>
        <name>TRANSCEIVER-1-1-C1</name>
        <config>
          <name>TRANSCEIVER-1-1-C1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>TRANSCEIVER-1-1-C5</name>
        <config>
          <name>TRANSCEIVER-1-1-C5</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_400GBASE_FR4</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:QSFPDD</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>OCH-1-7-L1</name>
        <optical-channel>
          <config>
            <frequency>0</frequency>
            <line-port>PORT-1-7-L1</line-port>
            <target-output-power>-5.0</target-output-power>
            <operational-mode>0</operational-mode>
          </config>
        </optical-channel>
        <config>
          <name>OCH-1-7-L1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>MCU-1-9</name>
        <subcomponents>
          <subcomponent>
            <name>PORT-1-9-NM1</name>
            <config>
              <name>PORT-1-9-NM1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-9-NM2</name>
            <config>
              <name>PORT-1-9-NM2</name>
            </config>
          </subcomponent>
        </subcomponents>
        <config>
          <remark>MCU-1-9</remark>
          <name>MCU-1-9</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S2UP</vendor-type>
          <led>DISABLED</led>
        </config>
      </component>
      <component>
        <name>FAN-1-31</name>
        <fan>
          <config/>
        </fan>
        <config>
          <remark>FAN-1-31</remark>
          <name>FAN-1-31</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S2FAN</vendor-type>
          <led>DISABLED</led>
        </config>
      </component>
      <component>
        <name>PORT-1-7-C1</name>
        <config>
          <name>PORT-1-7-C1</name>
          <remark>PORT-1-7-C1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-7-C1</name>
            <config>
              <name>TRANSCEIVER-1-7-C1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-9-NM1</name>
        <config>
          <name>PORT-1-9-NM1</name>
          <remark>PORT-1-9-NM1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-1-C4</name>
        <config>
          <name>TRANSCEIVER-1-1-C4</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-11-AUX</name>
        <config>
          <name>PORT-1-11-AUX</name>
          <remark>PORT-1-11-AUX</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-3-4-APSP</name>
        <config>
          <name>PORT-1-3-4-APSP</name>
          <remark>PORT-1-3-APSA4</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>FAN-1-32</name>
        <fan>
          <config/>
        </fan>
        <config>
          <remark>FAN-1-32</remark>
          <name>FAN-1-32</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S2FAN</vendor-type>
          <led>DISABLED</led>
        </config>
      </component>
      <component>
        <name>PORT-1-7-C2</name>
        <config>
          <name>PORT-1-7-C2</name>
          <remark>PORT-1-7-C2</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-7-C2</name>
            <config>
              <name>TRANSCEIVER-1-7-C2</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-9-NM2</name>
        <config>
          <name>PORT-1-9-NM2</name>
          <remark>PORT-1-9-NM2</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>OCH-1-8-L1</name>
        <optical-channel>
          <config>
            <frequency>0</frequency>
            <line-port>PORT-1-8-L1</line-port>
            <target-output-power>-5.0</target-output-power>
            <operational-mode>0</operational-mode>
          </config>
        </optical-channel>
        <config>
          <name>OCH-1-8-L1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>TRANSCEIVER-1-1-C3</name>
        <config>
          <name>TRANSCEIVER-1-1-C3</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <physical-channels>
            <channel>
              <index>3</index>
              <config>
                <tx-laser>true</tx-laser>
                <index>3</index>
                <description/>
              </config>
            </channel>
            <channel>
              <index>4</index>
              <config>
                <tx-laser>true</tx-laser>
                <index>4</index>
                <description/>
              </config>
            </channel>
            <channel>
              <index>1</index>
              <config>
                <tx-laser>true</tx-laser>
                <index>1</index>
                <description/>
              </config>
            </channel>
            <channel>
              <index>2</index>
              <config>
                <tx-laser>true</tx-laser>
                <index>2</index>
                <description/>
              </config>
            </channel>
          </physical-channels>
          <config>
            <ethernet-pmd-preconf>x:ETH_100GBASE_LR4D</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:QSFP28</form-factor-preconf>
            <fec-mode>x:FEC_DISABLED</fec-mode>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-3-3-APSP</name>
        <config>
          <name>PORT-1-3-3-APSP</name>
          <remark>PORT-1-3-APSA3</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-1-L1</name>
        <config>
          <name>TRANSCEIVER-1-1-L1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <enabled>true</enabled>
            <form-factor-preconf>x:CFP</form-factor-preconf>
            <fec-mode>x:FEC_ENABLED</fec-mode>
          </config>
        </transceiver>
      </component>
      <component>
        <name>TRANSCEIVER-1-1-C2</name>
        <config>
          <name>TRANSCEIVER-1-1-C2</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>TRANSCEIVER-1-1-C6</name>
        <config>
          <name>TRANSCEIVER-1-1-C6</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>TRANSCEIVER-1-8-L1</name>
        <config>
          <name>TRANSCEIVER-1-8-L1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:CFP2_DCO</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>LINECARD-1-8</name>
        <linecard>
          <config>
            <power-admin-state>POWER_ENABLED</power-admin-state>
          </config>
        </linecard>
        <config>
          <remark>LINECARD-1-8</remark>
          <name>LINECARD-1-8</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S4T400D</vendor-type>
          <led>DISABLED</led>
        </config>
        <subcomponents>
          <subcomponent>
            <name>PORT-1-8-C4</name>
            <config>
              <name>PORT-1-8-C4</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-8-C3</name>
            <config>
              <name>PORT-1-8-C3</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-8-C2</name>
            <config>
              <name>PORT-1-8-C2</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-8-C1</name>
            <config>
              <name>PORT-1-8-C1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-8-L1</name>
            <config>
              <name>PORT-1-8-L1</name>
            </config>
          </subcomponent>
        </subcomponents>
      </component>
      <component>
        <name>PORT-1-3-2-APSP</name>
        <config>
          <name>PORT-1-3-2-APSP</name>
          <remark>PORT-1-3-APSA2</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-12-AUX</name>
        <config>
          <name>PORT-1-12-AUX</name>
          <remark>PORT-1-12-AUX</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>LINECARD-1-7</name>
        <linecard>
          <config>
            <power-admin-state>POWER_ENABLED</power-admin-state>
          </config>
        </linecard>
        <config>
          <remark>LINECARD-1-7</remark>
          <name>LINECARD-1-7</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S4T400D</vendor-type>
          <led>DISABLED</led>
        </config>
        <subcomponents>
          <subcomponent>
            <name>PORT-1-7-C4</name>
            <config>
              <name>PORT-1-7-C4</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-7-C3</name>
            <config>
              <name>PORT-1-7-C3</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-7-C2</name>
            <config>
              <name>PORT-1-7-C2</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-7-C1</name>
            <config>
              <name>PORT-1-7-C1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-7-L1</name>
            <config>
              <name>PORT-1-7-L1</name>
            </config>
          </subcomponent>
        </subcomponents>
      </component>
      <component>
        <name>MCU-1-11</name>
        <config>
          <remark>MCU-1-11</remark>
          <name>MCU-1-11</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S2EC2</vendor-type>
        </config>
        <cpu>
          <utilization>
            <state>
              <instant>60</instant>
            </state>
          </utilization>
        </cpu>
        <subcomponents>
          <subcomponent>
            <name>PORT-1-11-CIT</name>
            <config>
              <name>PORT-1-11-CIT</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-11-AUX</name>
            <config>
              <name>PORT-1-11-AUX</name>
            </config>
          </subcomponent>
        </subcomponents>
      </component>
      <component>
        <name>PORT-1-12-CIT</name>
        <config>
          <name>PORT-1-12-CIT</name>
          <remark>PORT-1-12-CIT</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>FAN-1-33</name>
        <fan>
          <config/>
        </fan>
        <config>
          <remark>FAN-1-33</remark>
          <name>FAN-1-33</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S2FAN</vendor-type>
          <led>DISABLED</led>
        </config>
      </component>
      <component>
        <name>PORT-1-7-C3</name>
        <config>
          <name>PORT-1-7-C3</name>
          <remark>PORT-1-7-C3</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-7-C3</name>
            <config>
              <name>TRANSCEIVER-1-7-C3</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-4-L1</name>
        <config>
          <name>TRANSCEIVER-1-4-L1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:CFP2_DCO</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>MCU-1-12</name>
        <config>
          <remark>MCU-1-12</remark>
          <name>MCU-1-12</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S2EC2</vendor-type>
        </config>
        <cpu>
          <utilization>
            <state>
              <instant>75</instant>
            </state>
          </utilization>
        </cpu>
        <subcomponents>
          <subcomponent>
            <name>PORT-1-12-CIT</name>
            <config>
              <name>PORT-1-12-CIT</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-12-AUX</name>
            <config>
              <name>PORT-1-12-AUX</name>
            </config>
          </subcomponent>
        </subcomponents>
      </component>
      <component>
        <name>FAN-1-34</name>
        <fan>
          <config/>
        </fan>
        <config>
          <remark>FAN-1-34</remark>
          <name>FAN-1-34</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S2FAN</vendor-type>
          <led>DISABLED</led>
        </config>
      </component>
      <component>
        <name>PORT-1-7-C4</name>
        <config>
          <name>PORT-1-7-C4</name>
          <remark>PORT-1-7-C4</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-7-C4</name>
            <config>
              <name>TRANSCEIVER-1-7-C4</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-5-C1</name>
        <config>
          <name>TRANSCEIVER-1-5-C1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_100GBASE_LR4D</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:QSFP28</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-3-1-APSC</name>
        <config>
          <name>PORT-1-3-1-APSC</name>
          <remark>PORT-1-3-APSSIG1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-3-2-APSS</name>
        <config>
          <name>PORT-1-3-2-APSS</name>
          <remark>PORT-1-3-APSB2</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-5-C3</name>
        <config>
          <name>TRANSCEIVER-1-5-C3</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>TRANSCEIVER-1-5-C2</name>
        <config>
          <name>TRANSCEIVER-1-5-C2</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>OCH-1-4-L1</name>
        <optical-channel>
          <config>
            <frequency>0</frequency>
            <line-port>PORT-1-4-L1</line-port>
            <target-output-power>-5.0</target-output-power>
            <operational-mode>0</operational-mode>
          </config>
        </optical-channel>
        <config>
          <name>OCH-1-4-L1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>PORT-1-4-C2</name>
        <config>
          <name>PORT-1-4-C2</name>
          <remark>PORT-1-4-C2</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-4-C2</name>
            <config>
              <name>TRANSCEIVER-1-4-C2</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-3-4-APSS</name>
        <config>
          <name>PORT-1-3-4-APSS</name>
          <remark>PORT-1-3-APSB4</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-5-C4</name>
        <config>
          <name>TRANSCEIVER-1-5-C4</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_400GBASE_SR8</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:QSFPDD</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-4-C3</name>
        <config>
          <name>PORT-1-4-C3</name>
          <remark>PORT-1-4-C3</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-4-C3</name>
            <config>
              <name>TRANSCEIVER-1-4-C3</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-3-3-APSS</name>
        <config>
          <name>PORT-1-3-3-APSS</name>
          <remark>PORT-1-3-APSB3</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-4-C1</name>
        <config>
          <name>PORT-1-4-C1</name>
          <remark>PORT-1-4-C1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-4-C1</name>
            <config>
              <name>TRANSCEIVER-1-4-C1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-5-L1</name>
        <config>
          <name>PORT-1-5-L1</name>
          <remark>PORT-1-5-L1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>OCH-1-5-L1</name>
            <config>
              <name>OCH-1-5-L1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>TRANSCEIVER-1-5-L1</name>
            <config>
              <name>TRANSCEIVER-1-5-L1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-8-L1</name>
        <config>
          <name>PORT-1-8-L1</name>
          <remark>PORT-1-8-L1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-8-L1</name>
            <config>
              <name>TRANSCEIVER-1-8-L1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>OCH-1-8-L1</name>
            <config>
              <name>OCH-1-8-L1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-7-C3</name>
        <config>
          <name>TRANSCEIVER-1-7-C3</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_100GBASE_LR4D</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:QSFP28</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-8-C4</name>
        <config>
          <name>PORT-1-8-C4</name>
          <remark>PORT-1-8-C4</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-8-C4</name>
            <config>
              <name>TRANSCEIVER-1-8-C4</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>SUBSLOT-1-3-2</name>
        <subcomponents>
          <subcomponent>
            <name>APS-1-3-2</name>
            <config>
              <name>APS-1-3-2</name>
            </config>
          </subcomponent>
        </subcomponents>
        <config>
          <name>SUBSLOT-1-3-2</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>PORT-1-1-C2</name>
        <config>
          <name>PORT-1-1-C2</name>
          <remark>PORT-1-1-C2</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-1-C2</name>
            <config>
              <name>TRANSCEIVER-1-1-C2</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-7-C2</name>
        <config>
          <name>TRANSCEIVER-1-7-C2</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>SUBSLOT-1-3-1</name>
        <subcomponents>
          <subcomponent>
            <name>APS-1-3-1</name>
            <config>
              <name>APS-1-3-1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <config>
          <name>SUBSLOT-1-3-1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>OCH-1-5-L1</name>
        <optical-channel>
          <config>
            <frequency>0</frequency>
            <line-port>PORT-1-5-L1</line-port>
            <target-output-power>-5.0</target-output-power>
            <operational-mode>0</operational-mode>
          </config>
        </optical-channel>
        <config>
          <name>OCH-1-5-L1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>PORT-1-1-C1</name>
        <config>
          <name>PORT-1-1-C1</name>
          <remark>PORT-1-1-C1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-1-C1</name>
            <config>
              <name>TRANSCEIVER-1-1-C1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-1-C4</name>
        <config>
          <name>PORT-1-1-C4</name>
          <remark>PORT-1-1-C4</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-1-C4</name>
            <config>
              <name>TRANSCEIVER-1-1-C4</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-7-C4</name>
        <config>
          <name>TRANSCEIVER-1-7-C4</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_400GBASE_LR4</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:QSFPDD</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>OLP-1-3</name>
        <subcomponents>
          <subcomponent>
            <name>PORT-1-3-2-APSS</name>
            <config>
              <name>PORT-1-3-2-APSS</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-3-APSP</name>
            <config>
              <name>PORT-1-3-3-APSP</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>SUBSLOT-1-3-4</name>
            <config>
              <name>SUBSLOT-1-3-4</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-3-APSS</name>
            <config>
              <name>PORT-1-3-3-APSS</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>SUBSLOT-1-3-3</name>
            <config>
              <name>SUBSLOT-1-3-3</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-2-APSP</name>
            <config>
              <name>PORT-1-3-2-APSP</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-1-APSC</name>
            <config>
              <name>PORT-1-3-1-APSC</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-4-APSC</name>
            <config>
              <name>PORT-1-3-4-APSC</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>SUBSLOT-1-3-2</name>
            <config>
              <name>SUBSLOT-1-3-2</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>SUBSLOT-1-3-1</name>
            <config>
              <name>SUBSLOT-1-3-1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-4-APSP</name>
            <config>
              <name>PORT-1-3-4-APSP</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-2-APSC</name>
            <config>
              <name>PORT-1-3-2-APSC</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-3-APSC</name>
            <config>
              <name>PORT-1-3-3-APSC</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-1-APSS</name>
            <config>
              <name>PORT-1-3-1-APSS</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-4-APSS</name>
            <config>
              <name>PORT-1-3-4-APSS</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-3-1-APSP</name>
            <config>
              <name>PORT-1-3-1-APSP</name>
            </config>
          </subcomponent>
        </subcomponents>
        <config>
          <remark>OLP-1-3</remark>
          <name>OLP-1-3</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>OLP4</vendor-type>
          <led>DISABLED</led>
        </config>
      </component>
      <component>
        <name>PORT-1-1-C3</name>
        <config>
          <name>PORT-1-1-C3</name>
          <remark>PORT-1-1-C3</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-1-C3</name>
            <config>
              <name>TRANSCEIVER-1-1-C3</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <breakout-mode>
            <config>
              <channel-speed>x:SPEED_100GB</channel-speed>
              <num-channels>1</num-channels>
            </config>
          </breakout-mode>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-8-C2</name>
        <config>
          <name>TRANSCEIVER-1-8-C2</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-5-C1</name>
        <config>
          <name>PORT-1-5-C1</name>
          <remark>PORT-1-5-C1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-5-C1</name>
            <config>
              <name>TRANSCEIVER-1-5-C1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-1-C6</name>
        <config>
          <name>PORT-1-1-C6</name>
          <remark>PORT-1-1-C6</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-1-C6</name>
            <config>
              <name>TRANSCEIVER-1-1-C6</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-8-C1</name>
        <config>
          <name>TRANSCEIVER-1-8-C1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-8-C1</name>
        <config>
          <name>PORT-1-8-C1</name>
          <remark>PORT-1-8-C1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-8-C1</name>
            <config>
              <name>TRANSCEIVER-1-8-C1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-5-C2</name>
        <config>
          <name>PORT-1-5-C2</name>
          <remark>PORT-1-5-C2</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-5-C2</name>
            <config>
              <name>TRANSCEIVER-1-5-C2</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-1-L1</name>
        <config>
          <name>PORT-1-1-L1</name>
          <remark>PORT-1-1-L1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>OCH-1-1-L1</name>
            <config>
              <name>OCH-1-1-L1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>TRANSCEIVER-1-1-L1</name>
            <config>
              <name>TRANSCEIVER-1-1-L1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-1-C5</name>
        <config>
          <name>PORT-1-1-C5</name>
          <remark>PORT-1-1-C5</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-1-C5</name>
            <config>
              <name>TRANSCEIVER-1-1-C5</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-11-CIT</name>
        <config>
          <name>PORT-1-11-CIT</name>
          <remark>PORT-1-11-CIT</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-8-C2</name>
        <config>
          <name>PORT-1-8-C2</name>
          <remark>PORT-1-8-C2</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-8-C2</name>
            <config>
              <name>TRANSCEIVER-1-8-C2</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>SUBSLOT-1-3-4</name>
        <subcomponents>
          <subcomponent>
            <name>APS-1-3-4</name>
            <config>
              <name>APS-1-3-4</name>
            </config>
          </subcomponent>
        </subcomponents>
        <config>
          <name>SUBSLOT-1-3-4</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>PORT-1-5-C3</name>
        <config>
          <name>PORT-1-5-C3</name>
          <remark>PORT-1-5-C3</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-5-C3</name>
            <config>
              <name>TRANSCEIVER-1-5-C3</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-8-C3</name>
        <config>
          <name>PORT-1-8-C3</name>
          <remark>PORT-1-8-C3</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-8-C3</name>
            <config>
              <name>TRANSCEIVER-1-8-C3</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>SUBSLOT-1-3-3</name>
        <subcomponents>
          <subcomponent>
            <name>APS-1-3-3</name>
            <config>
              <name>APS-1-3-3</name>
            </config>
          </subcomponent>
        </subcomponents>
        <config>
          <name>SUBSLOT-1-3-3</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>PORT-1-5-C4</name>
        <config>
          <name>PORT-1-5-C4</name>
          <remark>PORT-1-5-C4</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-5-C4</name>
            <config>
              <name>TRANSCEIVER-1-5-C4</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>LINECARD-1-5</name>
        <linecard>
          <config>
            <power-admin-state>POWER_ENABLED</power-admin-state>
          </config>
        </linecard>
        <config>
          <remark>LINECARD-1-5</remark>
          <name>LINECARD-1-5</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S4T400D</vendor-type>
          <led>DISABLED</led>
        </config>
        <subcomponents>
          <subcomponent>
            <name>PORT-1-5-C3</name>
            <config>
              <name>PORT-1-5-C3</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-5-C4</name>
            <config>
              <name>PORT-1-5-C4</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-5-C1</name>
            <config>
              <name>PORT-1-5-C1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-5-C2</name>
            <config>
              <name>PORT-1-5-C2</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-5-L1</name>
            <config>
              <name>PORT-1-5-L1</name>
            </config>
          </subcomponent>
        </subcomponents>
      </component>
      <component>
        <name>PORT-1-3-2-APSC</name>
        <config>
          <name>PORT-1-3-2-APSC</name>
          <remark>PORT-1-3-APSSIG2</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>PORT-1-3-1-APSS</name>
        <config>
          <name>PORT-1-3-1-APSS</name>
          <remark>PORT-1-3-APSB1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-8-C4</name>
        <config>
          <name>TRANSCEIVER-1-8-C4</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:QSFPDD</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>TRANSCEIVER-1-4-C3</name>
        <config>
          <name>TRANSCEIVER-1-4-C3</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_100GBASE_LR4D</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:QSFP28</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-3-1-APSP</name>
        <config>
          <name>PORT-1-3-1-APSP</name>
          <remark>PORT-1-3-APSA1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>CHASSIS-1</name>
        <subcomponents>
          <subcomponent>
            <name>FAN-1-31</name>
            <config>
              <name>FAN-1-31</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>LINECARD-1-1</name>
            <config>
              <name>LINECARD-1-1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>FAN-1-32</name>
            <config>
              <name>FAN-1-32</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>FAN-1-33</name>
            <config>
              <name>FAN-1-33</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>LINECARD-1-4</name>
            <config>
              <name>LINECARD-1-4</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>FAN-1-34</name>
            <config>
              <name>FAN-1-34</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>LINECARD-1-5</name>
            <config>
              <name>LINECARD-1-5</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PSU-1-22</name>
            <config>
              <name>PSU-1-22</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PSU-1-21</name>
            <config>
              <name>PSU-1-21</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>OLP-1-3</name>
            <config>
              <name>OLP-1-3</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>MCU-1-9</name>
            <config>
              <name>MCU-1-9</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>LINECARD-1-7</name>
            <config>
              <name>LINECARD-1-7</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>LINECARD-1-8</name>
            <config>
              <name>LINECARD-1-8</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>MCU-1-11</name>
            <config>
              <name>MCU-1-11</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>MCU-1-12</name>
            <config>
              <name>MCU-1-12</name>
            </config>
          </subcomponent>
        </subcomponents>
        <config>
          <name>CHASSIS-1</name>
          <remark>main shelf</remark>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>TRANSCEIVER-1-8-C3</name>
        <config>
          <name>TRANSCEIVER-1-8-C3</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>OCH-1-1-L1</name>
        <optical-channel>
          <config>
            <frequency>191931250</frequency>
            <line-port>PORT-1-1-L1</line-port>
            <target-output-power>-5.0</target-output-power>
            <operational-mode>33</operational-mode>
          </config>
        </optical-channel>
        <config>
          <name>OCH-1-1-L1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>TRANSCEIVER-1-4-C4</name>
        <config>
          <name>TRANSCEIVER-1-4-C4</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_400GBASE_FR4</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:QSFPDD</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>LINECARD-1-4</name>
        <linecard>
          <config>
            <power-admin-state>POWER_ENABLED</power-admin-state>
          </config>
        </linecard>
        <config>
          <remark>LINECARD-1-4</remark>
          <name>LINECARD-1-4</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S4T400D</vendor-type>
          <led>DISABLED</led>
        </config>
        <subcomponents>
          <subcomponent>
            <name>PORT-1-4-C2</name>
            <config>
              <name>PORT-1-4-C2</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-4-C3</name>
            <config>
              <name>PORT-1-4-C3</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-4-L1</name>
            <config>
              <name>PORT-1-4-L1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-4-C1</name>
            <config>
              <name>PORT-1-4-C1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-4-C4</name>
            <config>
              <name>PORT-1-4-C4</name>
            </config>
          </subcomponent>
        </subcomponents>
      </component>
      <component>
        <name>PORT-1-7-L1</name>
        <config>
          <name>PORT-1-7-L1</name>
          <remark>PORT-1-7-L1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>OCH-1-7-L1</name>
            <config>
              <name>OCH-1-7-L1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>TRANSCEIVER-1-7-L1</name>
            <config>
              <name>TRANSCEIVER-1-7-L1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-5-L1</name>
        <config>
          <name>TRANSCEIVER-1-5-L1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:CFP2_DCO</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-4-C4</name>
        <config>
          <name>PORT-1-4-C4</name>
          <remark>PORT-1-4-C4</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>TRANSCEIVER-1-4-C4</name>
            <config>
              <name>TRANSCEIVER-1-4-C4</name>
            </config>
          </subcomponent>
        </subcomponents>
        <port>
          <config/>
        </port>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>TRANSCEIVER-1-4-C1</name>
        <config>
          <name>TRANSCEIVER-1-4-C1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_100GBASE_LR4D</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:QSFP28</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>LINECARD-1-1</name>
        <linecard>
          <config>
            <power-admin-state>POWER_ENABLED</power-admin-state>
          </config>
        </linecard>
        <config>
          <remark>LINECARD-1-1</remark>
          <name>LINECARD-1-1</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S6T600D</vendor-type>
          <led>DISABLED</led>
        </config>
        <subcomponents>
          <subcomponent>
            <name>PORT-1-1-C5</name>
            <config>
              <name>PORT-1-1-C5</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-1-C6</name>
            <config>
              <name>PORT-1-1-C6</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-1-L1</name>
            <config>
              <name>PORT-1-1-L1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-1-C1</name>
            <config>
              <name>PORT-1-1-C1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-1-C2</name>
            <config>
              <name>PORT-1-1-C2</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-1-C3</name>
            <config>
              <name>PORT-1-1-C3</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>PORT-1-1-C4</name>
            <config>
              <name>PORT-1-1-C4</name>
            </config>
          </subcomponent>
        </subcomponents>
      </component>
      <component>
        <name>PORT-1-3-3-APSC</name>
        <config>
          <name>PORT-1-3-3-APSC</name>
          <remark>PORT-1-3-APSSIG3</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>APS-1-3-4</name>
        <config>
          <name>APS-1-3-4</name>
          <remark>APS-1-3-4</remark>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>PSU-1-21</name>
        <config>
          <remark>PSU-1-21</remark>
          <name>PSU-1-21</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S2AC13</vendor-type>
        </config>
        <power-supply>
          <state>
            <enabled>true</enabled>
            <input-current>RFNszQ==</input-current>
            <input-voltage>Q1rAAA==</input-voltage>
            <output-current>Rl1vew==</output-current>
            <output-voltage>QUB64Q==</output-voltage>
            <output-power>QypAAA==</output-power>
            <capacity>RKKAAA==</capacity>
          </state>
          <config>
            <enabled>true</enabled>
          </config>
        </power-supply>
      </component>
      <component>
        <name>TRANSCEIVER-1-4-C2</name>
        <config>
          <name>TRANSCEIVER-1-4-C2</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-3-4-APSC</name>
        <config>
          <name>PORT-1-3-4-APSC</name>
          <remark>PORT-1-3-APSSIG4</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>APS-1-3-3</name>
        <config>
          <name>APS-1-3-3</name>
          <remark>APS-1-3-3</remark>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>PSU-1-22</name>
        <config>
          <remark>PSU-1-22</remark>
          <name>PSU-1-22</name>
          <admin-state>ENABLED</admin-state>
          <vendor-type>S2DC13</vendor-type>
        </config>
        <power-supply>
          <state>
            <enabled>true</enabled>
            <input-current>RVTkAA==</input-current>
            <input-voltage>QlR64Q==</input-voltage>
            <output-current>RlXeAA==</output-current>
            <output-voltage>QUHCjw==</output-voltage>
            <output-power>QyWAAA==</output-power>
            <capacity>RKKAAA==</capacity>
          </state>
          <config>
            <enabled>true</enabled>
          </config>
        </power-supply>
      </component>
      <component>
        <name>TRANSCEIVER-1-7-C1</name>
        <config>
          <name>TRANSCEIVER-1-7-C1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:OTHER</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>APS-1-3-2</name>
        <config>
          <name>APS-1-3-2</name>
          <remark>APS-1-3-2</remark>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
      <component>
        <name>TRANSCEIVER-1-7-L1</name>
        <config>
          <name>TRANSCEIVER-1-7-L1</name>
          <remark/>
          <admin-state>ENABLED</admin-state>
        </config>
        <transceiver>
          <config>
            <ethernet-pmd-preconf>x:ETH_UNDEFINED</ethernet-pmd-preconf>
            <enabled>true</enabled>
            <form-factor-preconf>x:CFP2_DCO</form-factor-preconf>
          </config>
        </transceiver>
      </component>
      <component>
        <name>PORT-1-4-L1</name>
        <config>
          <name>PORT-1-4-L1</name>
          <remark>PORT-1-4-L1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
        <subcomponents>
          <subcomponent>
            <name>OCH-1-4-L1</name>
            <config>
              <name>OCH-1-4-L1</name>
            </config>
          </subcomponent>
          <subcomponent>
            <name>TRANSCEIVER-1-4-L1</name>
            <config>
              <name>TRANSCEIVER-1-4-L1</name>
            </config>
          </subcomponent>
        </subcomponents>
        <optical-port>
          <config>
            <admin-state>ENABLED</admin-state>
          </config>
        </optical-port>
      </component>
      <component>
        <name>APS-1-3-1</name>
        <config>
          <name>APS-1-3-1</name>
          <remark>APS-1-3-1</remark>
          <admin-state>ENABLED</admin-state>
        </config>
      </component>
    </components>
    <pm-points>
      <pm-point>
        <name>PORT-1-3-4-APSS</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-4-APSP</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-3-APSC</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-7-C2</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-7-C1</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-7-C4</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-7-C3</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>FAN-1-31</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>LINECARD-1-5</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-3-APSP</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>LINECARD-1-4</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-4-APSC</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>LINECARD-1-7</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-4-L1</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>LINECARD-1-8</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-8-L1</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-3-APSS</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>ODUC3-1-1-L1</name>
        <state>
          <point-type>otn</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>severely-errored-seconds-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>4</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>4</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>unavailable-seconds-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>background-block-errors-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>559621</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>559621</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>background-block-errors-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>5829</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>5829</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>errored-seconds-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>unavailable-seconds-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>severely-errored-seconds-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>40</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>40</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>errored-seconds-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>250</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>250</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>LINECARD-1-1</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-4-C4</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>MCU-1-12</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>MCU-1-11</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-4-C3</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-4-C2</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-8-C3</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-8-C2</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-8-C4</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-L1</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-4-C1</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-8-C1</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>FAN-1-32</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>FAN-1-33</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-5-L1</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>FAN-1-34</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>OCH-1-7-L1</name>
        <state>
          <point-type>optical-channel</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>OTUC3-1-1-L1</name>
        <state>
          <point-type>otn</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>severely-errored-seconds-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>4</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>4</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>unavailable-seconds-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>background-block-errors-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>5829</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>5829</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>errored-seconds-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>250</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>250</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>pre-fec-ber-instant</pm-name>
            <pm-period>untimed</pm-period>
            <state>
              <thresholds>
                <threshold-high-max>1.0</threshold-high-max>
                <threshold-high>0.0199</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>0.0199</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>post-fec-ber-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>1E-10</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>1E-10</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>pre-fec-ber-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>0.024</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>0.024</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>background-block-errors-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>559621</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>559621</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>pre-fec-ber-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>0.024</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>0.024</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>unavailable-seconds-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>errored-seconds-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>post-fec-ber-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>1E-10</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>1E-10</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>severely-errored-seconds-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>40</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>40</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-C3-1</name>
        <state>
          <point-type>physical-channel</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-C3-2</name>
        <state>
          <point-type>physical-channel</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-C3-3</name>
        <state>
          <point-type>physical-channel</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-C3-4</name>
        <state>
          <point-type>physical-channel</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-7</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>131</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-11</threshold-low>
                <threshold-high>5</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-C1</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-C2</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-1-APSP</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-C5</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-5-C3</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-C6</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-5-C4</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-C3</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-1-C4</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>100GE-1-1-C3</name>
        <state>
          <point-type>ethernet</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>in-crc-errors-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>108000</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>108000</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>out-crc-errors-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>1130</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>1130</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>in-crc-errors-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>1130</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>1130</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>out-crc-errors-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>108000</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>108000</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-5-C1</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>OCH-1-8-L1</name>
        <state>
          <point-type>optical-channel</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-5-C2</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>OCH-1-1-L1</name>
        <state>
          <point-type>optical-channel</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-1-APSS</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>OCH-1-4-L1</name>
        <state>
          <point-type>optical-channel</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>CHASSIS-1</name>
        <state>
          <point-type>chassis</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>65</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>65</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>65</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>65</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PSU-1-22</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-1-APSC</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PSU-1-21</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-2-APSC</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>OCH-1-5-L1</name>
        <state>
          <point-type>optical-channel</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>7</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>laser-bias-current-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>0</threshold-low>
                <threshold-high>300</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>6</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>TRANSCEIVER-1-7-L1</name>
        <state>
          <point-type>transceiver</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-5</threshold-low>
                <threshold-high>77</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-20</threshold-low>
                <threshold-high>15</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-2-APSP</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>PORT-1-3-2-APSS</name>
        <state>
          <point-type>port</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>output-power-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>23</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>input-power-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-35</threshold-low>
                <threshold-high>26</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>ODU4-1-1-C3</name>
        <state>
          <point-type>otn</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>severely-errored-seconds-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>4</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>4</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>unavailable-seconds-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>background-block-errors-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>184980</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>184980</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>background-block-errors-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>1927</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>1927</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>errored-seconds-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-high>25</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>25</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>unavailable-seconds-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>10</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>severely-errored-seconds-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>40</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>40</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>errored-seconds-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-high>250</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-high>250</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
      <pm-point>
        <name>OLP-1-3</name>
        <state>
          <point-type>card</point-type>
        </state>
        <parameters>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>24h</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
          <parameter>
            <pm-name>temperature-instant</pm-name>
            <pm-period>15min</pm-period>
            <state>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </state>
            <config>
              <thresholds>
                <threshold-low>-10</threshold-low>
                <threshold-high>75</threshold-high>
              </thresholds>
            </config>
          </parameter>
        </parameters>
      </pm-point>
    </pm-points>
    <aps>
      <aps-modules>
        <aps-module>
          <name>APS-1-3-4</name>
          <ports>
            <line-primary-in>
              <config>
                <enabled>true</enabled>
              </config>
            </line-primary-in>
            <line-secondary-in>
              <config>
                <enabled>true</enabled>
              </config>
            </line-secondary-in>
            <line-primary-out>
              <config>
                <target-attenuation>0.0</target-attenuation>
              </config>
            </line-primary-out>
            <line-secondary-out>
              <config>
                <target-attenuation>0.0</target-attenuation>
              </config>
            </line-secondary-out>
            <common-output>
              <config/>
            </common-output>
            <common-in>
              <config>
                <enabled>true</enabled>
              </config>
            </common-in>
          </ports>
          <config>
            <revertive>false</revertive>
            <wait-to-restore-time>600000</wait-to-restore-time>
            <relative-switch-threshold>0.0</relative-switch-threshold>
            <primary-switch-threshold>-20.0</primary-switch-threshold>
            <secondary-switch-threshold>-20.0</secondary-switch-threshold>
            <name>APS-1-3-4</name>
            <hold-off-time>0</hold-off-time>
            <primary-switch-hysteresis>2.0</primary-switch-hysteresis>
            <relative-switch-threshold-offset>0.0</relative-switch-threshold-offset>
            <force-to-port>NONE</force-to-port>
          </config>
          <protected-optical-channels/>
        </aps-module>
        <aps-module>
          <name>APS-1-3-3</name>
          <ports>
            <line-primary-in>
              <config>
                <enabled>true</enabled>
              </config>
            </line-primary-in>
            <line-secondary-in>
              <config>
                <enabled>true</enabled>
              </config>
            </line-secondary-in>
            <line-primary-out>
              <config>
                <target-attenuation>0.0</target-attenuation>
              </config>
            </line-primary-out>
            <line-secondary-out>
              <config>
                <target-attenuation>0.0</target-attenuation>
              </config>
            </line-secondary-out>
            <common-output>
              <config/>
            </common-output>
            <common-in>
              <config>
                <enabled>true</enabled>
              </config>
            </common-in>
          </ports>
          <config>
            <revertive>false</revertive>
            <wait-to-restore-time>600000</wait-to-restore-time>
            <relative-switch-threshold>0.0</relative-switch-threshold>
            <primary-switch-threshold>-20.0</primary-switch-threshold>
            <secondary-switch-threshold>-20.0</secondary-switch-threshold>
            <name>APS-1-3-3</name>
            <hold-off-time>0</hold-off-time>
            <primary-switch-hysteresis>2.0</primary-switch-hysteresis>
            <relative-switch-threshold-offset>0.0</relative-switch-threshold-offset>
            <force-to-port>NONE</force-to-port>
          </config>
          <protected-optical-channels/>
        </aps-module>
        <aps-module>
          <name>APS-1-3-2</name>
          <ports>
            <line-primary-in>
              <config>
                <enabled>true</enabled>
              </config>
            </line-primary-in>
            <line-secondary-in>
              <config>
                <enabled>true</enabled>
              </config>
            </line-secondary-in>
            <line-primary-out>
              <config>
                <target-attenuation>0.0</target-attenuation>
              </config>
            </line-primary-out>
            <line-secondary-out>
              <config>
                <target-attenuation>0.0</target-attenuation>
              </config>
            </line-secondary-out>
            <common-output>
              <config/>
            </common-output>
            <common-in>
              <config>
                <enabled>true</enabled>
              </config>
            </common-in>
          </ports>
          <config>
            <revertive>false</revertive>
            <wait-to-restore-time>600000</wait-to-restore-time>
            <relative-switch-threshold>0.0</relative-switch-threshold>
            <primary-switch-threshold>-20.0</primary-switch-threshold>
            <secondary-switch-threshold>-20.0</secondary-switch-threshold>
            <name>APS-1-3-2</name>
            <hold-off-time>0</hold-off-time>
            <primary-switch-hysteresis>2.0</primary-switch-hysteresis>
            <relative-switch-threshold-offset>0.0</relative-switch-threshold-offset>
            <force-to-port>NONE</force-to-port>
          </config>
          <protected-optical-channels/>
        </aps-module>
        <aps-module>
          <name>APS-1-3-1</name>
          <ports>
            <line-primary-in>
              <config>
                <enabled>true</enabled>
              </config>
            </line-primary-in>
            <line-secondary-in>
              <config>
                <enabled>true</enabled>
              </config>
            </line-secondary-in>
            <line-primary-out>
              <config>
                <target-attenuation>0.0</target-attenuation>
              </config>
            </line-primary-out>
            <line-secondary-out>
              <config>
                <target-attenuation>0.0</target-attenuation>
              </config>
            </line-secondary-out>
            <common-output>
              <config/>
            </common-output>
            <common-in>
              <config>
                <enabled>true</enabled>
              </config>
            </common-in>
          </ports>
          <config>
            <revertive>false</revertive>
            <wait-to-restore-time>600000</wait-to-restore-time>
            <relative-switch-threshold>5.0</relative-switch-threshold>
            <primary-switch-threshold>-20.0</primary-switch-threshold>
            <secondary-switch-threshold>-20.0</secondary-switch-threshold>
            <name>APS-1-3-1</name>
            <hold-off-time>0</hold-off-time>
            <primary-switch-hysteresis>2.0</primary-switch-hysteresis>
            <relative-switch-threshold-offset>0.0</relative-switch-threshold-offset>
            <force-to-port>NONE</force-to-port>
          </config>
          <protected-optical-channels/>
        </aps-module>
      </aps-modules>
    </aps>
  </data>
</rpc-reply>
