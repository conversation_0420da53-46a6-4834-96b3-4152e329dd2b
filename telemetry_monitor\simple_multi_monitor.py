#!/usr/bin/env python3
"""
简化版多设备 Telemetry 监控器
修复了原版本的问题
"""

import socket
import json
import time
import threading
from datetime import datetime
from collections import defaultdict

class SimpleMultiDeviceMonitor:
    """简化版多设备监控器"""
    
    def __init__(self, host='0.0.0.0', port=57400):
        self.host = host
        self.port = port
        self.running = False
        self.server_socket = None
        self.client_connections = []  # 改名避免冲突
        self.total_data_count = 0
        self.start_time = None
        
        # 设备统计信息
        self.device_stats = defaultdict(lambda: {
            'message_count': 0,
            'last_seen': None,
            'recent_messages': []  # 使用简单列表
        })
        
        # 全局统计
        self.active_devices = set()
    
    def start(self):
        """启动监控器"""
        
        print("🌟 简化版多设备 Telemetry 监控器")
        print(f"📡 监听地址: {self.host}:{self.port}")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔍 等待多设备连接...")
        print("=" * 80)
        
        try:
            # 创建服务器套接字
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(20)
            
            self.running = True
            self.start_time = datetime.now()
            
            # 启动状态报告线程
            status_thread = threading.Thread(target=self._status_reporter)
            status_thread.daemon = True
            status_thread.start()
            
            while self.running:
                try:
                    # 接受连接
                    client_socket, client_address = self.server_socket.accept()
                    print(f"\n🔗 新连接: {client_address}")
                    print(f"⏰ 连接时间: {datetime.now().strftime('%H:%M:%S')}")
                    
                    self.client_connections.append(client_socket)
                    
                    # 为每个客户端启动处理线程
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ 接受连接失败: {e}")
                    
        except Exception as e:
            print(f"❌ 启动监控器失败: {e}")
        
        finally:
            try:
                if self.server_socket:
                    self.server_socket.close()
            except:
                pass
    
    def _handle_client(self, client_socket, client_address):
        """处理客户端数据"""
        
        buffer = ""
        
        try:
            while self.running:
                data = client_socket.recv(8192)
                if not data:
                    break
                
                try:
                    decoded_data = data.decode('utf-8')
                    buffer += decoded_data
                    
                    # 处理完整的消息
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        if line.strip():
                            self._process_message(line.strip(), client_address)
                
                except UnicodeDecodeError:
                    # 处理二进制数据
                    self._process_binary_message(data, client_address)
        
        except Exception as e:
            print(f"❌ 处理客户端 {client_address} 数据失败: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            try:
                client_socket.close()
                if client_socket in self.client_connections:
                    self.client_connections.remove(client_socket)
                print(f"🔌 客户端 {client_address} 断开连接")
            except:
                pass
    
    def _process_message(self, data, client_address):
        """处理文本消息"""
        
        self.total_data_count += 1
        timestamp = datetime.now()
        
        # 尝试解析 JSON
        try:
            json_data = json.loads(data)
            device_name = json_data.get('device', f'unknown-{client_address[0]}')
            
            # 更新设备统计
            self._update_device_stats(device_name, json_data, timestamp, client_address)
            
            # 显示消息
            self._display_device_message(device_name, json_data, timestamp, client_address)
            
        except json.JSONDecodeError:
            device_name = f'unknown-{client_address[0]}'
            print(f"\n📝 [{timestamp.strftime('%H:%M:%S')}] 原始数据来自 {device_name}")
            print(f"   数据: {data[:100]}...")
        
        # 保存数据
        self._save_data(data, timestamp, client_address)
    
    def _update_device_stats(self, device_name, data, timestamp, client_address):
        """更新设备统计信息"""
        
        stats = self.device_stats[device_name]
        stats['message_count'] += 1
        stats['last_seen'] = timestamp
        
        # 添加到最近消息列表
        stats['recent_messages'].append({
            'timestamp': timestamp,
            'data': data,
            'client_address': client_address
        })
        
        # 只保留最近50条消息
        if len(stats['recent_messages']) > 50:
            stats['recent_messages'] = stats['recent_messages'][-50:]
        
        # 更新活跃设备集合
        self.active_devices.add(device_name)
    
    def _display_device_message(self, device_name, data, timestamp, client_address):
        """显示设备消息"""
        
        device_count = self.device_stats[device_name]['message_count']
        timestamp_str = timestamp.strftime('%H:%M:%S.%f')[:-3]
        
        print(f"\n📊 [{timestamp_str}] 设备: {device_name} | 消息 #{device_count} | 来自: {client_address}")
        print("=" * 80)
        
        # 显示基本信息
        if 'sensor_group' in data:
            print(f"📡 传感器组: {data['sensor_group']}")
        
        if 'subscription' in data:
            print(f"📋 订阅: {data['subscription']}")
        
        if 'sequence' in data:
            print(f"🔢 序列号: {data['sequence']}")
        
        # 提取关键信息
        self._extract_and_display_key_info(data)
        
        print("-" * 60)
    
    def _extract_and_display_key_info(self, data):
        """提取并显示关键信息"""
        
        try:
            # 查找 CPU 利用率
            cpu_info = self._find_cpu_info(data)
            if cpu_info:
                print(f"💻 CPU 利用率: {cpu_info}")
            
            # 查找光收发器信息
            transceiver_info = self._find_transceiver_info(data)
            if transceiver_info:
                print(f"🔌 光收发器: {transceiver_info}")
            
            # 查找逻辑通道信息
            channel_info = self._find_channel_info(data)
            if channel_info:
                print(f"📊 逻辑通道: {channel_info}")
        
        except Exception as e:
            print(f"⚠️ 信息提取失败: {e}")
    
    def _find_cpu_info(self, data):
        """查找 CPU 信息"""
        def search_cpu(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if 'cpu' in key.lower() and 'utilization' in key.lower():
                        if isinstance(value, dict) and 'state' in value:
                            instant = value['state'].get('instant')
                            if instant is not None:
                                return f"{instant}%"
                        elif isinstance(value, dict) and 'instant' in value:
                            return f"{value['instant']}%"
                    elif isinstance(value, (dict, list)):
                        result = search_cpu(value)
                        if result:
                            return result
            elif isinstance(obj, list):
                for item in obj:
                    result = search_cpu(item)
                    if result:
                        return result
            return None
        
        return search_cpu(data)
    
    def _find_transceiver_info(self, data):
        """查找光收发器信息"""
        def search_transceiver(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if 'transceiver' in key.lower():
                        if isinstance(value, dict):
                            # 查找输出功率
                            power = self._find_nested_value(value, ['output-power', 'output_power'])
                            if power is not None:
                                return f"功率 {power} dBm"
                    elif isinstance(value, (dict, list)):
                        result = search_transceiver(value)
                        if result:
                            return result
            elif isinstance(obj, list):
                for item in obj:
                    result = search_transceiver(item)
                    if result:
                        return result
            return None
        
        return search_transceiver(data)
    
    def _find_channel_info(self, data):
        """查找通道信息"""
        otn_count = 0
        eth_count = 0
        
        def count_channels(obj):
            nonlocal otn_count, eth_count
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if 'otn' in key.lower():
                        otn_count += 1
                    elif 'ethernet' in key.lower():
                        eth_count += 1
                    elif isinstance(value, (dict, list)):
                        count_channels(value)
            elif isinstance(obj, list):
                for item in obj:
                    count_channels(item)
        
        count_channels(data)
        
        if otn_count > 0 or eth_count > 0:
            return f"OTN: {otn_count}, 以太网: {eth_count}"
        return None
    
    def _find_nested_value(self, obj, keys):
        """在嵌套对象中查找值"""
        if isinstance(obj, dict):
            for key in keys:
                if key in obj:
                    return obj[key]
            for value in obj.values():
                if isinstance(value, (dict, list)):
                    result = self._find_nested_value(value, keys)
                    if result is not None:
                        return result
        elif isinstance(obj, list):
            for item in obj:
                result = self._find_nested_value(item, keys)
                if result is not None:
                    return result
        return None
    
    def _process_binary_message(self, data, client_address):
        """处理二进制消息"""
        
        self.total_data_count += 1
        timestamp = datetime.now()
        
        print(f"\n📊 [{timestamp.strftime('%H:%M:%S')}] 二进制数据来自 {client_address}")
        print(f"📏 数据长度: {len(data)} 字节")
    
    def _save_data(self, data, timestamp, client_address):
        """保存数据到文件"""
        try:
            filename = f"simple_multi_device_{datetime.now().strftime('%Y%m%d')}.log"
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp.strftime('%H:%M:%S.%f')[:-3]}] {client_address}: {data}\n")
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def _status_reporter(self):
        """状态报告线程"""
        while self.running:
            time.sleep(30)  # 每30秒报告一次
            if self.running and self.start_time:
                self._print_status_report()
    
    def _print_status_report(self):
        """打印状态报告"""
        runtime = datetime.now() - self.start_time
        
        print(f"\n📊 === 多设备监控状态报告 ===")
        print(f"⏱️  运行时间: {runtime}")
        print(f"📨 总消息数: {self.total_data_count}")
        print(f"🔗 活跃连接: {len(self.client_connections)}")
        print(f"📱 活跃设备: {len(self.active_devices)}")
        
        # 按设备显示统计
        if self.active_devices:
            print(f"\n📋 设备详情:")
            for device_name in sorted(self.active_devices):
                stats = self.device_stats[device_name]
                last_seen = stats['last_seen']
                time_since = datetime.now() - last_seen if last_seen else None
                
                if time_since and time_since.total_seconds() < 60:
                    status = "🟢 活跃"
                elif time_since and time_since.total_seconds() < 300:
                    status = "🟡 空闲"
                else:
                    status = "🔴 离线"
                
                print(f"   {device_name}: {status} | 消息: {stats['message_count']} | 最后: {last_seen.strftime('%H:%M:%S') if last_seen else 'N/A'}")
        
        print("=" * 50)
    
    def stop(self):
        """停止监控器"""
        self.running = False
        
        for client in self.client_connections:
            try:
                client.close()
            except:
                pass
        self.client_connections.clear()
        
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        print("🛑 简化版多设备监控器已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简化版多设备 Telemetry 监控器')
    parser.add_argument('--host', default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=57400, help='监听端口')
    
    args = parser.parse_args()
    
    monitor = SimpleMultiDeviceMonitor(args.host, args.port)
    
    try:
        monitor.start()
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        monitor.stop()
    except Exception as e:
        print(f"监控器运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
