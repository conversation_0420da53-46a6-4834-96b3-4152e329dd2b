#!/usr/bin/env python3
"""
连接测试工具
用于测试收集器的连接性
"""

import socket
import time
import json
from datetime import datetime

def test_connection(host='127.0.0.1', port=57400):
    """测试基本连接"""
    
    print(f"🔍 测试连接到 {host}:{port}")
    
    try:
        # 创建套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        # 尝试连接
        print(f"⏰ {datetime.now().strftime('%H:%M:%S')} - 尝试连接...")
        sock.connect((host, port))
        print(f"✅ {datetime.now().strftime('%H:%M:%S')} - 连接成功！")
        
        # 发送简单消息
        message = "Hello from connection test\n"
        print(f"📤 {datetime.now().strftime('%H:%M:%S')} - 发送测试消息...")
        sock.send(message.encode('utf-8'))
        print(f"✅ {datetime.now().strftime('%H:%M:%S')} - 消息发送成功！")
        
        # 等待一下
        time.sleep(1)
        
        # 关闭连接
        sock.close()
        print(f"🔌 {datetime.now().strftime('%H:%M:%S')} - 连接已关闭")
        
        return True
        
    except ConnectionRefusedError:
        print(f"❌ {datetime.now().strftime('%H:%M:%S')} - 连接被拒绝！收集器可能没有运行或端口不正确")
        return False
    except socket.timeout:
        print(f"❌ {datetime.now().strftime('%H:%M:%S')} - 连接超时！")
        return False
    except Exception as e:
        print(f"❌ {datetime.now().strftime('%H:%M:%S')} - 连接失败: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

def test_json_message(host='127.0.0.1', port=57400):
    """测试发送 JSON 消息"""
    
    print(f"\n📊 测试发送 JSON 消息到 {host}:{port}")
    
    try:
        # 创建套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        # 连接
        print(f"⏰ {datetime.now().strftime('%H:%M:%S')} - 连接中...")
        sock.connect((host, port))
        print(f"✅ {datetime.now().strftime('%H:%M:%S')} - 已连接")
        
        # 创建测试 JSON 消息
        test_message = {
            "timestamp": int(time.time() * 1000),
            "device": "test-device",
            "sensor_group": "test-group",
            "sequence": 1,
            "data": {
                "test": "This is a test message",
                "cpu_utilization": 25.5,
                "temperature": 42.0
            }
        }
        
        # 发送 JSON 消息
        json_data = json.dumps(test_message) + "\n"
        print(f"📤 {datetime.now().strftime('%H:%M:%S')} - 发送 JSON 消息...")
        print(f"📄 消息内容: {json_data[:100]}...")
        
        sock.send(json_data.encode('utf-8'))
        print(f"✅ {datetime.now().strftime('%H:%M:%S')} - JSON 消息发送成功！")
        
        # 等待一下
        time.sleep(2)
        
        # 关闭连接
        sock.close()
        print(f"🔌 {datetime.now().strftime('%H:%M:%S')} - 连接已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ {datetime.now().strftime('%H:%M:%S')} - JSON 消息发送失败: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

def test_multiple_connections(host='127.0.0.1', port=57400, count=3):
    """测试多个连接"""
    
    print(f"\n🔄 测试多个连接到 {host}:{port}")
    
    success_count = 0
    
    for i in range(count):
        print(f"\n--- 测试连接 #{i+1} ---")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            
            # 连接
            sock.connect((host, port))
            print(f"✅ 连接 #{i+1} 成功")
            
            # 发送消息
            message = f"Test message #{i+1} from connection test\n"
            sock.send(message.encode('utf-8'))
            print(f"📤 消息 #{i+1} 发送成功")
            
            success_count += 1
            
            # 短暂等待
            time.sleep(0.5)
            
            # 关闭连接
            sock.close()
            print(f"🔌 连接 #{i+1} 已关闭")
            
        except Exception as e:
            print(f"❌ 连接 #{i+1} 失败: {e}")
        
        # 连接间隔
        if i < count - 1:
            time.sleep(1)
    
    print(f"\n📊 测试结果: {success_count}/{count} 连接成功")
    return success_count == count

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='连接测试工具')
    parser.add_argument('--host', default='127.0.0.1', help='目标主机地址')
    parser.add_argument('--port', type=int, default=57400, help='目标端口')
    parser.add_argument('--test', choices=['basic', 'json', 'multiple', 'all'], 
                       default='all', help='测试类型')
    
    args = parser.parse_args()
    
    print("🧪 收集器连接测试工具")
    print("=" * 50)
    
    if args.test in ['basic', 'all']:
        print("\n🔍 === 基本连接测试 ===")
        basic_result = test_connection(args.host, args.port)
        
        if not basic_result:
            print("\n❌ 基本连接失败，请检查:")
            print("   1. 收集器是否正在运行")
            print("   2. 端口是否正确")
            print("   3. 防火墙设置")
            return 1
    
    if args.test in ['json', 'all']:
        print("\n📊 === JSON 消息测试 ===")
        json_result = test_json_message(args.host, args.port)
        
        if not json_result:
            print("\n❌ JSON 消息发送失败")
            return 1
    
    if args.test in ['multiple', 'all']:
        print("\n🔄 === 多连接测试 ===")
        multiple_result = test_multiple_connections(args.host, args.port, 3)
        
        if not multiple_result:
            print("\n❌ 多连接测试失败")
            return 1
    
    print("\n🎉 === 所有测试通过 ===")
    print("✅ 收集器连接正常")
    print("💡 如果真实设备仍无法连接，请检查设备配置和网络设置")
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
