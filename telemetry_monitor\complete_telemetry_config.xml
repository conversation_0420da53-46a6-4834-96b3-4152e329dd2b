
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <sensor-groups>
      <sensor-group>
        <sensor-group-id>allSg</sensor-group-id>
        <config>
          <sensor-group-id>allSg</sensor-group-id>
        </config>
        <sensor-paths>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/state</path>
            <config>
              <path>/openconfig-platform:components/component/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
            <config>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
            <config>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
            <config>
              <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
            </config>
          </sensor-path>
        </sensor-paths>
      </sensor-group>
    </sensor-groups>

    <destination-groups>
      <destination-group>
        <group-id>optical-collector</group-id>
        <config>
          <group-id>optical-collector</group-id>
        </config>
        <destinations>
          <destination>
            <destination-address>***************</destination-address>
            <destination-port>57400</destination-port>
            <config>
              <destination-address>***************</destination-address>
              <destination-port>57400</destination-port>
            </config>
          </destination>
        </destinations>
      </destination-group>
    </destination-groups>


  </telemetry-system>
</config>
