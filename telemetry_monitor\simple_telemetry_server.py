#!/usr/bin/env python3
"""
简单的 Telemetry 服务器
支持多种协议：TCP Socket, HTTP, 和基础 gRPC 处理
"""

import socket
import json
import time
import threading
import os
from datetime import datetime
from collections import defaultdict
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse

class TelemetryHandler(BaseHTTPRequestHandler):
    """HTTP 请求处理器"""
    
    def do_POST(self):
        """处理 POST 请求"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            client_ip = self.client_address[0]
            timestamp = datetime.now()
            
            print(f"📡 HTTP POST from {client_ip}: {len(post_data)} bytes")
            
            # 尝试解析 JSON
            try:
                json_data = json.loads(post_data.decode('utf-8'))
                print(f"✅ 解析到 JSON 数据: {json.dumps(json_data, indent=2)[:200]}...")
            except:
                print(f"📦 原始数据: {post_data[:200]}...")
            
            # 保存数据
            self.server.save_data(client_ip, {
                'timestamp': timestamp.isoformat(),
                'method': 'HTTP_POST',
                'content_type': self.headers.get('Content-Type', 'unknown'),
                'data_length': len(post_data),
                'data': post_data.decode('utf-8', errors='replace')[:1000]
            })
            
            # 响应成功
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(b'{"status": "ok"}')
            
        except Exception as e:
            print(f"❌ HTTP 处理错误: {e}")
            self.send_response(500)
            self.end_headers()
    
    def log_message(self, format, *args):
        """禁用默认日志"""
        pass

class SimpleTelemetryServer:
    """简单的 Telemetry 服务器"""
    
    def __init__(self, tcp_port=57400, http_port=8080, data_dir='telemetry_data'):
        self.tcp_port = tcp_port
        self.http_port = http_port
        self.data_dir = data_dir
        self.running = False
        
        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 设备统计信息
        self.device_stats = defaultdict(lambda: {
            'message_count': 0,
            'last_seen': None,
            'first_seen': None
        })
        
        self.total_messages = 0
    
    def save_data(self, client_ip, data):
        """保存数据到文件"""
        try:
            device_name = self._get_device_name(client_ip)
            date_str = datetime.now().strftime('%Y%m%d')
            filename = f"{self.data_dir}/{device_name}_{date_str}.json"
            
            with open(filename, 'a', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
                f.write('\n')
            
            # 更新统计
            self.device_stats[device_name]['message_count'] += 1
            self.device_stats[device_name]['last_seen'] = datetime.now()
            if self.device_stats[device_name]['first_seen'] is None:
                self.device_stats[device_name]['first_seen'] = datetime.now()
            
            self.total_messages += 1
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def _get_device_name(self, ip_address):
        """根据IP地址获取设备名称"""
        device_mapping = {
            '***************': 'nokia-alu-1',
            '***************': 'nokia-alu-2', 
            '***************': 'nokia-alu-3',
            '***************': 'nokia-alu-4'
        }
        return device_mapping.get(ip_address, f'unknown-{ip_address}')
    
    def start_tcp_server(self):
        """启动 TCP 服务器"""
        try:
            tcp_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            tcp_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            tcp_socket.bind(('0.0.0.0', self.tcp_port))
            tcp_socket.listen(10)
            
            print(f"🔗 TCP 服务器启动: 0.0.0.0:{self.tcp_port}")
            
            while self.running:
                try:
                    client_socket, client_address = tcp_socket.accept()
                    print(f"🔗 TCP 连接: {client_address}")
                    
                    # 启动客户端处理线程
                    client_thread = threading.Thread(
                        target=self._handle_tcp_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ TCP 接受连接失败: {e}")
        
        except Exception as e:
            print(f"❌ TCP 服务器启动失败: {e}")
        finally:
            try:
                tcp_socket.close()
            except:
                pass
    
    def _handle_tcp_client(self, client_socket, client_address):
        """处理 TCP 客户端"""
        buffer = b""
        
        try:
            client_socket.settimeout(30.0)
            
            while self.running:
                try:
                    data = client_socket.recv(8192)
                    if not data:
                        print(f"📡 TCP 客户端 {client_address} 发送空数据")
                        break
                    
                    buffer += data
                    print(f"📦 TCP 从 {client_address} 接收: {len(data)} 字节")
                    
                    # 尝试处理数据
                    if len(buffer) > 0:
                        # 检查是否是 gRPC 格式
                        if len(buffer) >= 5 and buffer[0] in [0, 1]:  # gRPC 压缩标志
                            self._handle_grpc_data(buffer, client_address)
                        else:
                            # 尝试作为文本处理
                            try:
                                text_data = buffer.decode('utf-8')
                                self._handle_text_data(text_data, client_address)
                            except:
                                self._handle_binary_data(buffer, client_address)
                        
                        buffer = b""  # 清空缓冲区
                
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"❌ TCP 处理错误: {e}")
                    break
        
        except Exception as e:
            print(f"❌ TCP 客户端处理失败: {e}")
        finally:
            try:
                client_socket.close()
                print(f"🔌 TCP 客户端 {client_address} 断开")
            except:
                pass
    
    def _handle_grpc_data(self, data, client_address):
        """处理 gRPC 数据"""
        try:
            if len(data) >= 5:
                compressed = data[0]
                message_length = int.from_bytes(data[1:5], 'big')
                
                print(f"📋 gRPC 消息: 压缩={compressed}, 长度={message_length}")
                
                if len(data) >= 5 + message_length:
                    message_data = data[5:5+message_length]
                    
                    self.save_data(client_address[0], {
                        'timestamp': datetime.now().isoformat(),
                        'method': 'TCP_GRPC',
                        'compressed': bool(compressed),
                        'message_length': message_length,
                        'data': message_data.hex()[:500]
                    })
        except Exception as e:
            print(f"❌ gRPC 数据处理失败: {e}")
    
    def _handle_text_data(self, text_data, client_address):
        """处理文本数据"""
        print(f"📝 文本数据: {text_data[:100]}...")
        
        self.save_data(client_address[0], {
            'timestamp': datetime.now().isoformat(),
            'method': 'TCP_TEXT',
            'data': text_data[:1000]
        })
    
    def _handle_binary_data(self, binary_data, client_address):
        """处理二进制数据"""
        print(f"🔢 二进制数据: {len(binary_data)} 字节")
        
        self.save_data(client_address[0], {
            'timestamp': datetime.now().isoformat(),
            'method': 'TCP_BINARY',
            'data_length': len(binary_data),
            'data_hex': binary_data.hex()[:500]
        })
    
    def start_http_server(self):
        """启动 HTTP 服务器"""
        try:
            server = HTTPServer(('0.0.0.0', self.http_port), TelemetryHandler)
            server.save_data = self.save_data  # 传递保存数据的方法
            
            print(f"🌐 HTTP 服务器启动: 0.0.0.0:{self.http_port}")
            
            while self.running:
                server.handle_request()
        
        except Exception as e:
            print(f"❌ HTTP 服务器启动失败: {e}")
    
    def start(self):
        """启动服务器"""
        print("🌟 简单 Telemetry 服务器")
        print(f"🔗 TCP 端口: {self.tcp_port}")
        print(f"🌐 HTTP 端口: {self.http_port}")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        self.running = True
        
        # 启动 TCP 服务器线程
        tcp_thread = threading.Thread(target=self.start_tcp_server)
        tcp_thread.daemon = True
        tcp_thread.start()
        
        # 启动 HTTP 服务器线程
        http_thread = threading.Thread(target=self.start_http_server)
        http_thread.daemon = True
        http_thread.start()
        
        # 启动状态报告线程
        status_thread = threading.Thread(target=self._status_reporter)
        status_thread.daemon = True
        status_thread.start()
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⚠️ 收到中断信号")
        finally:
            self.stop()
    
    def _status_reporter(self):
        """状态报告线程"""
        while self.running:
            try:
                time.sleep(30)  # 每30秒报告一次
                if self.running and self.total_messages > 0:
                    print(f"\n📊 状态报告 ({datetime.now().strftime('%H:%M:%S')})")
                    print(f"📦 总消息数: {self.total_messages}")
                    for device, stats in self.device_stats.items():
                        if stats['message_count'] > 0:
                            print(f"   {device}: {stats['message_count']} 消息")
            except:
                pass
    
    def stop(self):
        """停止服务器"""
        print("\n🛑 正在停止服务器...")
        self.running = False
        print("✅ 服务器已停止")

def main():
    """主函数"""
    server = SimpleTelemetryServer()
    server.start()

if __name__ == "__main__":
    main()
