#!/usr/bin/env python3
"""
Nokia ALU 设备测试脚本
测试与真实 Nokia ALU 设备的连接和 Telemetry 配置功能
"""

import sys
import os
import logging

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

from src.telemetry_config import TelemetryConfigurator
from src.utils import setup_logging


def test_nokia_connection():
    """测试 Nokia 设备连接"""
    print("=== Nokia ALU 设备连接测试 ===\n")
    
    # 设置详细日志
    setup_logging("DEBUG", "nokia_test.log")
    
    try:
        # 初始化配置器
        print("1. 初始化 Telemetry 配置器...")
        configurator = TelemetryConfigurator(
            device_config_file="config/device_config.yaml",
            telemetry_config_file="config/telemetry_config.yaml"
        )
        print("✓ 配置器初始化成功\n")
        
        # 列出设备
        print("2. 列出配置的设备...")
        devices = configurator.netconf_client.list_devices()
        print(f"找到 {len(devices)} 个设备:")
        for device in devices:
            info = configurator.netconf_client.get_device_info(device)
            print(f"  - {device}: {info['host']}:{info['port']} ({info['device_type']})")
        print()
        
        # 测试 Nokia 设备连接
        nokia_device = "nokia-alu-1"
        print(f"3. 测试 Nokia 设备 '{nokia_device}' 连接...")
        
        try:
            if configurator.netconf_client.validate_connection(nokia_device):
                print("✓ Nokia 设备连接成功!")
                
                # 获取设备能力
                print("4. 获取设备能力...")
                capabilities = configurator.netconf_client.get_device_capabilities(nokia_device)
                print(f"设备支持 {len(capabilities)} 个 NETCONF 能力:")
                
                # 显示前 10 个能力
                for i, cap in enumerate(capabilities[:10]):
                    print(f"  {i+1}. {cap}")
                if len(capabilities) > 10:
                    print(f"  ... 还有 {len(capabilities) - 10} 个能力")
                print()
                
                # 检查 Telemetry 相关能力
                print("5. 检查 Telemetry 支持...")
                telemetry_caps = [cap for cap in capabilities if 'telemetry' in cap.lower()]
                openconfig_caps = [cap for cap in capabilities if 'openconfig' in cap.lower()]
                
                if telemetry_caps:
                    print("✓ 发现 Telemetry 相关能力:")
                    for cap in telemetry_caps:
                        print(f"  - {cap}")
                else:
                    print("⚠ 未发现明确的 Telemetry 能力")
                
                if openconfig_caps:
                    print("✓ 发现 OpenConfig 相关能力:")
                    for cap in openconfig_caps[:5]:  # 只显示前5个
                        print(f"  - {cap}")
                    if len(openconfig_caps) > 5:
                        print(f"  ... 还有 {len(openconfig_caps) - 5} 个 OpenConfig 能力")
                else:
                    print("⚠ 未发现 OpenConfig 能力")
                print()
                
                # 尝试获取当前配置
                print("6. 尝试获取设备配置...")
                try:
                    config = configurator.netconf_client.get_config(nokia_device, 'running')
                    print(f"✓ 成功获取配置 ({len(config)} 字符)")
                    
                    # 保存配置到文件以供检查
                    with open("nokia_current_config.xml", "w", encoding="utf-8") as f:
                        f.write(config)
                    print("✓ 配置已保存到 nokia_current_config.xml")
                    
                except Exception as e:
                    print(f"⚠ 获取配置失败: {e}")
                print()
                
                # 验证配置
                print("7. 验证设备配置...")
                validation_results = configurator.validate_configuration(nokia_device)
                
                print("验证结果:")
                print(f"  设备连接: {'✓' if validation_results['device_connection'] else '✗'}")
                print(f"  Telemetry 支持: {'✓' if validation_results['telemetry_support'] else '✗'}")
                print(f"  配置有效: {'✓' if validation_results['configuration_valid'] else '✗'}")
                
                if validation_results['errors']:
                    print("  错误信息:")
                    for error in validation_results['errors']:
                        print(f"    - {error}")
                print()
                
                return True
                
            else:
                print("✗ Nokia 设备连接失败")
                return False
                
        except Exception as e:
            print(f"✗ 连接测试失败: {e}")
            logging.exception("连接测试异常")
            return False
        
    except Exception as e:
        print(f"错误: {e}")
        logging.exception("测试脚本异常")
        return False
    
    finally:
        # 清理资源
        try:
            configurator.cleanup()
        except:
            pass


def test_telemetry_configuration():
    """测试 Telemetry 配置功能"""
    print("\n=== Nokia ALU Telemetry 配置测试 ===\n")
    
    try:
        configurator = TelemetryConfigurator(
            device_config_file="config/device_config.yaml",
            telemetry_config_file="config/telemetry_config.yaml"
        )
        
        nokia_device = "nokia-alu-1"
        
        # 添加测试收集器
        print("1. 添加测试收集器...")
        configurator.add_collector(
            name="nokia-test-collector",
            address="*************",
            port=57400,
            protocol="grpc",
            encoding="json"
        )
        print("✓ 测试收集器添加成功\n")
        
        # 列出可用模板
        print("2. 列出可用的监控模板...")
        templates = configurator.list_available_templates()
        for template in templates:
            info = configurator.get_template_info(template)
            print(f"  - {template}: {info['description']}")
            print(f"    类别: {', '.join(info['categories'])}")
            print(f"    采样间隔: {info['sample_interval']}ms")
            print(f"    总指标数: {info['total_metrics']}")
        print()
        
        # 生成配置（不实际应用）
        print("3. 生成基础监控配置...")
        
        # 创建传感器组
        sensor_groups = configurator.create_sensor_groups_from_template('basic')
        print(f"✓ 创建了 {len(sensor_groups)} 个传感器组:")
        for sg in sensor_groups:
            print(f"  - {sg.group_id}: {len(sg.paths)} 个监控路径")
        print()
        
        # 生成完整配置 XML
        print("4. 生成完整的 XML 配置...")
        
        sensor_groups_config = []
        for sg in sensor_groups:
            sensor_groups_config.append({
                'group_id': sg.group_id,
                'paths': sg.paths
            })
        
        destination_groups_config = [{
            'group_id': 'nokia-test-collector',
            'destinations': [{
                'address': '*************',
                'port': 57400,
                'protocol': 'grpc',
                'encoding': 'json'
            }]
        }]
        
        subscriptions_config = []
        for sg in sensor_groups:
            subscriptions_config.append({
                'subscription_id': f'sub_{sg.group_id}',
                'sensor_group': sg.group_id,
                'destination_group': 'nokia-test-collector',
                'sample_interval': 30000,
                'source_address': '',
                'qos_marking': 0,
                'suppress_redundant': 'false',
                'heartbeat_interval': 60000
            })
        
        xml_config = configurator.yang_templates.generate_complete_telemetry_config(
            sensor_groups=sensor_groups_config,
            destination_groups=destination_groups_config,
            subscriptions=subscriptions_config
        )
        
        # 保存生成的配置
        with open("nokia_telemetry_config.xml", "w", encoding="utf-8") as f:
            f.write(xml_config)
        
        print(f"✓ 生成完整 XML 配置 ({len(xml_config)} 字符)")
        print("✓ 配置已保存到 nokia_telemetry_config.xml")
        print()
        
        # 询问是否应用配置
        print("5. 配置应用选项...")
        print("生成的配置文件可以手动检查后应用到设备")
        print("如需自动应用，请取消注释下面的代码")
        
        # 注释掉实际配置应用，避免意外修改设备
        """
        apply_config = input("是否要应用配置到设备？(y/N): ").lower().strip()
        if apply_config == 'y':
            print("正在应用配置到设备...")
            success = configurator.configure_telemetry(
                device_name=nokia_device,
                template_name="basic",
                collector_name="nokia-test-collector",
                sample_interval=30000
            )
            
            if success:
                print("✓ 配置应用成功")
            else:
                print("✗ 配置应用失败")
        else:
            print("跳过配置应用")
        """
        
        return True
        
    except Exception as e:
        print(f"配置测试失败: {e}")
        logging.exception("配置测试异常")
        return False
    
    finally:
        try:
            configurator.cleanup()
        except:
            pass


def main():
    """主函数"""
    print("Nokia ALU 设备测试脚本")
    print("=" * 50)
    
    # 测试连接
    connection_success = test_nokia_connection()
    
    if connection_success:
        # 测试配置功能
        config_success = test_telemetry_configuration()
        
        if config_success:
            print("\n=== 测试总结 ===")
            print("✓ Nokia 设备连接测试通过")
            print("✓ Telemetry 配置生成测试通过")
            print("\n生成的文件:")
            print("- nokia_test.log: 详细测试日志")
            print("- nokia_current_config.xml: 设备当前配置")
            print("- nokia_telemetry_config.xml: 生成的 Telemetry 配置")
            print("\n建议:")
            print("1. 检查生成的 XML 配置文件")
            print("2. 根据设备实际支持的 YANG 模型调整配置")
            print("3. 在测试环境中验证配置后再应用到生产设备")
            return 0
        else:
            print("\n✗ 配置测试失败")
            return 1
    else:
        print("\n✗ 连接测试失败，请检查设备配置和网络连接")
        return 1


if __name__ == "__main__":
    sys.exit(main())