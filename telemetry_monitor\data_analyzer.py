#!/usr/bin/env python3
"""
Telemetry 数据分析工具
用于分析和可视化收集到的 telemetry 数据
"""

import json
import os
import re
from datetime import datetime
from typing import List, Dict, Any
import argparse

class TelemetryDataAnalyzer:
    """Telemetry 数据分析器"""
    
    def __init__(self, data_file: str = None):
        """
        初始化分析器
        
        Args:
            data_file: 数据文件路径
        """
        if data_file is None:
            # 查找最新的数据文件
            today = datetime.now().strftime('%Y%m%d')
            data_file = f"telemetry_data_{today}.log"
        
        self.data_file = data_file
        self.data_entries = []
        
        if os.path.exists(data_file):
            self.load_data()
        else:
            print(f"数据文件 {data_file} 不存在")
    
    def load_data(self):
        """加载数据文件"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line:
                        entry = self.parse_log_entry(line, line_num)
                        if entry:
                            self.data_entries.append(entry)
            
            print(f"成功加载 {len(self.data_entries)} 条数据记录")
            
        except Exception as e:
            print(f"加载数据文件失败: {e}")
    
    def parse_log_entry(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析日志条目"""
        try:
            # 解析格式: [timestamp] (client_address): data
            pattern = r'\[([^\]]+)\] \(([^)]+)\): (.+)'
            match = re.match(pattern, line)
            
            if not match:
                print(f"第 {line_num} 行格式不正确: {line}")
                return None
            
            timestamp_str, client_address, data_str = match.groups()
            
            # 解析时间戳
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
            
            # 尝试解析 JSON 数据
            try:
                data = json.loads(data_str)
                data_type = 'json'
            except json.JSONDecodeError:
                data = data_str
                data_type = 'text'
            
            return {
                'line_num': line_num,
                'timestamp': timestamp,
                'client_address': client_address,
                'data_type': data_type,
                'data': data
            }
            
        except Exception as e:
            print(f"解析第 {line_num} 行失败: {e}")
            return None
    
    def show_summary(self):
        """显示数据摘要"""
        if not self.data_entries:
            print("没有数据可分析")
            return
        
        print("\n=== 数据摘要 ===")
        print(f"总记录数: {len(self.data_entries)}")
        
        # 按数据类型分组
        json_count = sum(1 for entry in self.data_entries if entry['data_type'] == 'json')
        text_count = len(self.data_entries) - json_count
        
        print(f"JSON 数据: {json_count} 条")
        print(f"文本数据: {text_count} 条")
        
        # 时间范围
        if self.data_entries:
            start_time = min(entry['timestamp'] for entry in self.data_entries)
            end_time = max(entry['timestamp'] for entry in self.data_entries)
            print(f"时间范围: {start_time} 到 {end_time}")
        
        # 客户端地址统计
        clients = {}
        for entry in self.data_entries:
            client = entry['client_address']
            clients[client] = clients.get(client, 0) + 1
        
        print(f"客户端地址:")
        for client, count in clients.items():
            print(f"  {client}: {count} 条记录")
    
    def show_json_data(self):
        """显示 JSON 数据详情"""
        json_entries = [entry for entry in self.data_entries if entry['data_type'] == 'json']
        
        if not json_entries:
            print("没有 JSON 数据")
            return
        
        print(f"\n=== JSON 数据详情 ({len(json_entries)} 条) ===")
        
        for i, entry in enumerate(json_entries, 1):
            print(f"\n记录 {i} (第 {entry['line_num']} 行):")
            print(f"  时间: {entry['timestamp']}")
            print(f"  客户端: {entry['client_address']}")
            print(f"  数据: {json.dumps(entry['data'], indent=2, ensure_ascii=False)}")
    
    def show_text_data(self):
        """显示文本数据详情"""
        text_entries = [entry for entry in self.data_entries if entry['data_type'] == 'text']
        
        if not text_entries:
            print("没有文本数据")
            return
        
        print(f"\n=== 文本数据详情 ({len(text_entries)} 条) ===")
        
        for i, entry in enumerate(text_entries, 1):
            print(f"\n记录 {i} (第 {entry['line_num']} 行):")
            print(f"  时间: {entry['timestamp']}")
            print(f"  客户端: {entry['client_address']}")
            print(f"  数据: {entry['data']}")
    
    def analyze_metrics(self):
        """分析指标数据"""
        json_entries = [entry for entry in self.data_entries if entry['data_type'] == 'json']
        
        if not json_entries:
            print("没有 JSON 数据可分析")
            return
        
        print(f"\n=== 指标分析 ===")
        
        # 收集所有指标
        all_metrics = {}
        device_metrics = {}
        
        for entry in json_entries:
            data = entry['data']
            
            # 设备统计
            if 'device' in data:
                device = data['device']
                if device not in device_metrics:
                    device_metrics[device] = []
                device_metrics[device].append(entry)
            
            # 指标统计
            for key, value in data.items():
                if isinstance(value, (int, float)):
                    if key not in all_metrics:
                        all_metrics[key] = []
                    all_metrics[key].append(value)
        
        # 显示设备统计
        if device_metrics:
            print(f"设备数据统计:")
            for device, entries in device_metrics.items():
                print(f"  {device}: {len(entries)} 条记录")
        
        # 显示指标统计
        if all_metrics:
            print(f"\n数值指标统计:")
            for metric, values in all_metrics.items():
                if values:
                    avg_val = sum(values) / len(values)
                    min_val = min(values)
                    max_val = max(values)
                    print(f"  {metric}:")
                    print(f"    数量: {len(values)}")
                    print(f"    平均值: {avg_val:.2f}")
                    print(f"    最小值: {min_val}")
                    print(f"    最大值: {max_val}")
    
    def export_csv(self, output_file: str = None):
        """导出为 CSV 格式"""
        if output_file is None:
            output_file = f"telemetry_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        try:
            import csv
            
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入表头
                writer.writerow(['时间戳', '客户端地址', '数据类型', '原始数据'])
                
                # 写入数据
                for entry in self.data_entries:
                    data_str = json.dumps(entry['data'], ensure_ascii=False) if entry['data_type'] == 'json' else entry['data']
                    writer.writerow([
                        entry['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
                        entry['client_address'],
                        entry['data_type'],
                        data_str
                    ])
            
            print(f"数据已导出到: {output_file}")
            
        except Exception as e:
            print(f"导出 CSV 失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Telemetry 数据分析工具')
    parser.add_argument('--file', '-f', help='数据文件路径')
    parser.add_argument('--summary', '-s', action='store_true', help='显示数据摘要')
    parser.add_argument('--json', '-j', action='store_true', help='显示 JSON 数据')
    parser.add_argument('--text', '-t', action='store_true', help='显示文本数据')
    parser.add_argument('--metrics', '-m', action='store_true', help='分析指标数据')
    parser.add_argument('--export', '-e', help='导出为 CSV 文件')
    parser.add_argument('--all', '-a', action='store_true', help='显示所有信息')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = TelemetryDataAnalyzer(args.file)
    
    if not analyzer.data_entries:
        print("没有数据可分析")
        return 1
    
    # 根据参数执行相应操作
    if args.all or args.summary:
        analyzer.show_summary()
    
    if args.all or args.json:
        analyzer.show_json_data()
    
    if args.all or args.text:
        analyzer.show_text_data()
    
    if args.all or args.metrics:
        analyzer.analyze_metrics()
    
    if args.export:
        analyzer.export_csv(args.export)
    
    # 如果没有指定任何选项，显示摘要
    if not any([args.summary, args.json, args.text, args.metrics, args.export, args.all]):
        analyzer.show_summary()
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
