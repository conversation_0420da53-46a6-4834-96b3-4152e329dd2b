#!/usr/bin/env python3
"""
测试数据发送器
用于测试 telemetry 收集器功能
"""

import socket
import json
import time
from datetime import datetime

def send_test_data(device_name="nokia-alu-1", collector_ip="***************", collector_port=57400, count=5):
    """发送测试数据"""
    
    print(f"🧪 测试数据发送器")
    print(f"📱 设备名称: {device_name}")
    print(f"📡 目标收集器: {collector_ip}:{collector_port}")
    print(f"📊 发送数量: {count} 条消息")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    for i in range(count):
        try:
            # 连接到收集器
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((collector_ip, collector_port))
            
            # 生成测试数据
            message = {
                "timestamp": int(time.time() * 1000),
                "device": device_name,
                "sensor_group": "allSg",
                "subscription": "telemetry-monitoring",
                "sequence": i + 1,
                "data": {
                    "openconfig-platform:components": {
                        "component": [
                            {
                                "name": f"transceiver-1/1/{(i % 4) + 1}",
                                "openconfig-platform-transceiver:transceiver": {
                                    "state": {
                                        "present": "PRESENT",
                                        "enabled": True,
                                        "fault-condition": False,
                                        "form-factor": "QSFP28",
                                        "vendor": "Nokia",
                                        "vendor-part": "3HE12345AA",
                                        "serial-no": f"ABC{123456789 + i}",
                                        "connector-type": "LC"
                                    },
                                    "physical-channels": {
                                        "channel": [
                                            {
                                                "index": 0,
                                                "state": {
                                                    "index": 0,
                                                    "description": "Lane 0",
                                                    "tx-laser": True,
                                                    "output-power": round(-2.0 + (i % 10) * 0.1, 2),
                                                    "input-power": round(-3.0 + (i % 8) * 0.1, 2),
                                                    "laser-bias-current": round(40.0 + (i % 20), 1),
                                                    "target-output-power": -2.0,
                                                    "temperature": round(35.0 + (i % 15), 1)
                                                }
                                            }
                                        ]
                                    }
                                }
                            },
                            {
                                "name": "optical-channel-1/1/1/1",
                                "openconfig-terminal-device:optical-channel": {
                                    "state": {
                                        "frequency": 196100000 + (i % 100) * 1000,
                                        "target-output-power": 0.0,
                                        "operational-mode": 1,
                                        "line-port": "port-1/1/1"
                                    }
                                }
                            },
                            {
                                "name": "cpu-0",
                                "cpu": {
                                    "openconfig-platform-cpu:utilization": {
                                        "state": {
                                            "instant": round(20.0 + (i % 30), 1),
                                            "avg": round(25.0 + (i % 15), 1),
                                            "min": 15.0,
                                            "max": 45.0,
                                            "interval": 300000000
                                        }
                                    }
                                }
                            }
                        ]
                    },
                    "openconfig-terminal-device:terminal-device": {
                        "logical-channels": {
                            "channel": [
                                {
                                    "index": 1,
                                    "otn": {
                                        "state": {
                                            "tributary-slot-granularity": "TRIB_SLOT_1_25G",
                                            "pre-fec-ber": {
                                                "instant": round((1.0 + (i % 5) * 0.1) * 1e-12, 15),
                                                "avg": 1.1e-12,
                                                "min": 9.8e-13,
                                                "max": 1.5e-12
                                            },
                                            "post-fec-ber": {
                                                "instant": 0.0,
                                                "avg": 0.0,
                                                "min": 0.0,
                                                "max": 0.0
                                            },
                                            "q-value": {
                                                "instant": round(15.0 + (i % 10) * 0.1, 1),
                                                "avg": 15.1,
                                                "min": 14.8,
                                                "max": 15.5
                                            }
                                        }
                                    },
                                    "ethernet": {
                                        "state": {
                                            "in-mac-control-frames": (i % 100),
                                            "in-mac-pause-frames": 0,
                                            "in-oversize-frames": (i % 10),
                                            "in-undersize-frames": 0,
                                            "in-fragment-frames": 0,
                                            "in-8021q-frames": 1000000 + i * 1000,
                                            "out-mac-control-frames": (i % 95),
                                            "out-mac-pause-frames": 0,
                                            "out-8021q-frames": 1000000 + i * 950
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
            
            # 发送数据
            json_data = json.dumps(message) + "\n"
            sock.send(json_data.encode('utf-8'))
            sock.close()
            
            # 显示发送状态
            timestamp = datetime.now().strftime('%H:%M:%S')
            cpu_util = message['data']['openconfig-platform:components']['component'][2]['cpu']['openconfig-platform-cpu:utilization']['state']['instant']
            output_power = message['data']['openconfig-platform:components']['component'][0]['openconfig-platform-transceiver:transceiver']['physical-channels']['channel'][0]['state']['output-power']
            
            print(f"📊 [{timestamp}] 发送消息 #{i+1}")
            print(f"   📱 设备: {device_name}")
            print(f"   💻 CPU: {cpu_util}%")
            print(f"   📡 输出功率: {output_power} dBm")
            print(f"   🌈 频率: {message['data']['openconfig-platform:components']['component'][1]['openconfig-terminal-device:optical-channel']['state']['frequency']} MHz")
            print("-" * 40)
            
            time.sleep(2)  # 每2秒发送一次
            
        except Exception as e:
            print(f"❌ 发送消息 #{i+1} 失败: {e}")
            time.sleep(1)
    
    print(f"✅ 测试完成，共发送 {count} 条消息")

def send_continuous_data(device_name="nokia-alu-1", collector_ip="***************", collector_port=57400, interval=10):
    """持续发送数据"""
    
    print(f"🔄 持续数据发送器")
    print(f"📱 设备名称: {device_name}")
    print(f"📡 目标收集器: {collector_ip}:{collector_port}")
    print(f"⏰ 发送间隔: {interval} 秒")
    print(f"🚀 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("按 Ctrl+C 停止发送")
    print("=" * 60)
    
    counter = 0
    
    try:
        while True:
            counter += 1
            
            try:
                # 连接到收集器
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect((collector_ip, collector_port))
                
                # 生成数据（简化版）
                message = {
                    "timestamp": int(time.time() * 1000),
                    "device": device_name,
                    "sensor_group": "allSg",
                    "subscription": "telemetry-monitoring",
                    "sequence": counter,
                    "data": {
                        "openconfig-platform:components": {
                            "component": [
                                {
                                    "name": "cpu-0",
                                    "cpu": {
                                        "openconfig-platform-cpu:utilization": {
                                            "state": {
                                                "instant": round(20.0 + (counter % 30), 1),
                                                "avg": round(25.0 + (counter % 15), 1)
                                            }
                                        }
                                    }
                                },
                                {
                                    "name": f"transceiver-1/1/{(counter % 4) + 1}",
                                    "openconfig-platform-transceiver:transceiver": {
                                        "physical-channels": {
                                            "channel": [
                                                {
                                                    "index": 0,
                                                    "state": {
                                                        "output-power": round(-2.0 + (counter % 10) * 0.1, 2),
                                                        "input-power": round(-3.0 + (counter % 8) * 0.1, 2),
                                                        "temperature": round(35.0 + (counter % 15), 1)
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
                
                # 发送数据
                json_data = json.dumps(message) + "\n"
                sock.send(json_data.encode('utf-8'))
                sock.close()
                
                # 显示发送状态
                timestamp = datetime.now().strftime('%H:%M:%S')
                cpu_util = message['data']['openconfig-platform:components']['component'][0]['cpu']['openconfig-platform-cpu:utilization']['state']['instant']
                output_power = message['data']['openconfig-platform:components']['component'][1]['openconfig-platform-transceiver:transceiver']['physical-channels']['channel'][0]['state']['output-power']
                
                print(f"📊 [{timestamp}] 发送消息 #{counter} | CPU: {cpu_util}% | 功率: {output_power} dBm")
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"❌ 发送消息 #{counter} 失败: {e}")
                time.sleep(5)  # 失败后等待5秒重试
                
    except KeyboardInterrupt:
        print(f"\n🛑 停止发送数据，共发送 {counter} 条消息")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试数据发送器')
    parser.add_argument('mode', choices=['test', 'continuous'], 
                       help='模式: test=发送测试数据, continuous=持续发送数据')
    parser.add_argument('--device', default='nokia-alu-1', help='设备名称')
    parser.add_argument('--collector-ip', default='***************', help='收集器IP地址')
    parser.add_argument('--collector-port', type=int, default=57400, help='收集器端口')
    parser.add_argument('--count', type=int, default=5, help='测试模式下发送的消息数量')
    parser.add_argument('--interval', type=int, default=10, help='持续模式下的发送间隔（秒）')
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'test':
            send_test_data(args.device, args.collector_ip, args.collector_port, args.count)
        elif args.mode == 'continuous':
            send_continuous_data(args.device, args.collector_ip, args.collector_port, args.interval)
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
