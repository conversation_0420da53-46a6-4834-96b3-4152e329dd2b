#!/usr/bin/env python3
"""
统一的 Telemetry 配置管理器
支持单个或多个设备的配置管理
"""

import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

from src.netconf_client import NetconfClient
from src.utils import setup_logging

class TelemetryConfigManager:
    """统一的 Telemetry 配置管理器"""
    
    def __init__(self, device_config_file="config/device_config.yaml"):
        self.client = NetconfClient(device_config_file)
        self.results = {}
        
        # 统一的 Telemetry 配置模板 (基于 odccAllonePsg-e.xml)
        self.telemetry_config_template = '''
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <destination-groups>
      <destination-group>
        <group-id>***************</group-id>
        <config>
          <group-id>***************</group-id>
        </config>
        <destinations>
          <destination>
            <destination-address>***************</destination-address>
            <destination-port>57400</destination-port>
            <config>
              <destination-address>***************</destination-address>
              <destination-port>57400</destination-port>
            </config>
          </destination>
        </destinations>
      </destination-group>
    </destination-groups>
    <sensor-groups>
      <sensor-group>
        <sensor-group-id>allSg</sensor-group-id>
        <config>
          <sensor-group-id>allSg</sensor-group-id>
        </config>
        <sensor-paths>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/state</path>
            <config>
              <path>/openconfig-platform:components/component/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
            <state>
              <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
            </state>
            <config>
              <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
            <state>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
            </state>
            <config>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
            <state>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
            </state>
            <config>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
            <state>
              <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
            </state>
            <config>
              <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
            </config>
          </sensor-path>
        </sensor-paths>
      </sensor-group>
    </sensor-groups>
    <subscriptions>
      <persistent-subscriptions>
        <persistent-subscription>
          <name>allPsg</name>
          <config>
            <name>allPsg</name>
            <local-source-address>***************</local-source-address>
            <protocol xmlns:x="http://openconfig.net/yang/telemetry-types">x:STREAM_JSON_RPC</protocol>
            <encoding xmlns:x="http://openconfig.net/yang/telemetry-types">x:ENC_JSON_IETF</encoding>
          </config>
          <sensor-profiles>
            <sensor-profile>
              <sensor-group>allSg</sensor-group>
              <config>
                <sensor-group>allSg</sensor-group>
                <sample-interval>5000</sample-interval>
                <heartbeat-interval>15000</heartbeat-interval>
                <suppress-redundant>false</suppress-redundant>
              </config>
            </sensor-profile>
          </sensor-profiles>
          <destination-groups>
            <destination-group>
              <group-id>***************</group-id>
              <config>
                <group-id>***************</group-id>
              </config>
            </destination-group>
          </destination-groups>
        </persistent-subscription>
      </persistent-subscriptions>
    </subscriptions>
  </telemetry-system>
</config>
'''
    
    def get_available_devices(self):
        """获取可用设备列表"""
        return list(self.client.devices.keys())
    
    def test_device_connection(self, device_name):
        """测试单个设备连接"""
        try:
            result = self.client.validate_connection(device_name)
            self.results[device_name] = {
                'connection': 'success' if result else 'failed',
                'timestamp': datetime.now(),
                'error': None
            }
            return result
        except Exception as e:
            self.results[device_name] = {
                'connection': 'error',
                'timestamp': datetime.now(),
                'error': str(e)
            }
            return False
    
    def configure_device_telemetry(self, device_name):
        """配置单个设备的 telemetry"""
        try:
            print(f"🔧 [{datetime.now().strftime('%H:%M:%S')}] 配置设备: {device_name}")
            
            # 测试连接
            if not self.client.validate_connection(device_name):
                raise Exception("设备连接失败")
            
            # 应用配置
            with self.client.get_connection(device_name) as conn:
                result = conn.edit_config(target='running', config=self.telemetry_config_template)
                
            self.results[device_name] = {
                'connection': 'success',
                'configuration': 'success',
                'timestamp': datetime.now(),
                'error': None
            }
            
            print(f"✅ [{datetime.now().strftime('%H:%M:%S')}] 设备 {device_name} 配置成功")
            return True
            
        except Exception as e:
            self.results[device_name] = {
                'connection': 'unknown',
                'configuration': 'failed',
                'timestamp': datetime.now(),
                'error': str(e)
            }
            
            print(f"❌ [{datetime.now().strftime('%H:%M:%S')}] 设备 {device_name} 配置失败: {e}")
            return False
    
    def check_device_status(self, device_name):
        """检查单个设备的 telemetry 状态"""
        try:
            with self.client.get_connection(device_name) as conn:
                filter_xml = '''
                <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
                  <sensor-groups/>
                  <destination-groups/>
                  <subscriptions/>
                </telemetry-system>
                '''
                
                result = conn.get_config(source='running', filter=('subtree', filter_xml))
                config_str = str(result)
                
                status = {
                    'sensor_groups': 'allSg' in config_str,
                    'destination_groups': '***************' in config_str,
                    'subscriptions': 'allPsg' in config_str,
                    'timestamp': datetime.now(),
                    'error': None
                }
                
                if device_name not in self.results:
                    self.results[device_name] = {}
                self.results[device_name].update(status)
                
                return status
                
        except Exception as e:
            status = {
                'sensor_groups': False,
                'destination_groups': False,
                'subscriptions': False,
                'timestamp': datetime.now(),
                'error': str(e)
            }
            
            if device_name not in self.results:
                self.results[device_name] = {}
            self.results[device_name].update(status)
            
            return status
    
    def clean_device_config(self, device_name):
        """清理单个设备的 telemetry 配置"""
        try:
            print(f"🗑️ [{datetime.now().strftime('%H:%M:%S')}] 清理设备: {device_name}")

            # 分步删除配置，先删除订阅 (使用正确的 NETCONF 格式)
            delete_subscription = '''
<config xmlns:xc="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <subscriptions>
      <persistent-subscriptions>
        <persistent-subscription xc:operation="delete">
          <name>allPsg</name>
        </persistent-subscription>
      </persistent-subscriptions>
    </subscriptions>
  </telemetry-system>
</config>
'''

            # 删除传感器组
            delete_sensor_group = '''
<config xmlns:xc="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <sensor-groups>
      <sensor-group xc:operation="delete">
        <sensor-group-id>allSg</sensor-group-id>
      </sensor-group>
    </sensor-groups>
  </telemetry-system>
</config>
'''

            # 删除目标组
            delete_destination_group = '''
<config xmlns:xc="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <destination-groups>
      <destination-group xc:operation="delete">
        <group-id>***************</group-id>
      </destination-group>
    </destination-groups>
  </telemetry-system>
</config>
'''

            with self.client.get_connection(device_name) as conn:
                # 按顺序删除：先删除订阅，再删除传感器组，最后删除目标组
                print(f"   删除订阅配置...")
                try:
                    conn.edit_config(target='running', config=delete_subscription)
                except Exception as e:
                    print(f"   订阅删除失败: {e}")

                print(f"   删除传感器组...")
                try:
                    conn.edit_config(target='running', config=delete_sensor_group)
                except Exception as e:
                    print(f"   传感器组删除失败: {e}")

                print(f"   删除目标组...")
                try:
                    conn.edit_config(target='running', config=delete_destination_group)
                except Exception as e:
                    print(f"   目标组删除失败: {e}")

            self.results[device_name] = {
                'connection': 'success',
                'cleanup': 'success',
                'timestamp': datetime.now(),
                'error': None
            }

            print(f"✅ [{datetime.now().strftime('%H:%M:%S')}] 设备 {device_name} 清理成功")
            return True

        except Exception as e:
            # 打印详细的错误信息用于调试
            import traceback
            error_details = f"{type(e).__name__}: {str(e)}"
            print(f"🔍 [{datetime.now().strftime('%H:%M:%S')}] 设备 {device_name} 详细错误: {error_details}")
            print(f"🔍 [{datetime.now().strftime('%H:%M:%S')}] 错误堆栈: {traceback.format_exc()}")

            self.results[device_name] = {
                'connection': 'unknown',
                'cleanup': 'failed',
                'timestamp': datetime.now(),
                'error': error_details
            }

            print(f"❌ [{datetime.now().strftime('%H:%M:%S')}] 设备 {device_name} 清理失败: {error_details}")
            return False
    
    def batch_operation(self, operation, device_names=None, max_workers=3):
        """批量操作"""
        if device_names is None:
            device_names = self.get_available_devices()
        
        operation_map = {
            'test': self.test_device_connection,
            'configure': self.configure_device_telemetry,
            'check': self.check_device_status,
            'clean': self.clean_device_config
        }
        
        if operation not in operation_map:
            raise ValueError(f"不支持的操作: {operation}")
        
        operation_func = operation_map[operation]
        operation_name = {
            'test': '测试连接',
            'configure': '配置 telemetry',
            'check': '检查状态',
            'clean': '清理配置'
        }[operation]
        
        print(f"🚀 开始批量{operation_name}: {len(device_names)} 个设备")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_device = {
                executor.submit(operation_func, device): device 
                for device in device_names
            }
            
            for future in as_completed(future_to_device):
                device = future_to_device[future]
                try:
                    result = future.result()
                    if operation == 'test':
                        status = "✅ 成功" if result else "❌ 失败"
                        print(f"   {device}: {status}")
                    elif operation == 'check':
                        sensor_status = "✅" if result['sensor_groups'] else "❌"
                        dest_status = "✅" if result['destination_groups'] else "❌"
                        sub_status = "✅" if result.get('subscriptions', False) else "❌"
                        print(f"   {device}: 传感器组 {sensor_status}, 目标组 {dest_status}, 订阅 {sub_status}")
                except Exception as e:
                    print(f"   {device}: ❌ 错误 - {e}")
        
        return self.results
    
    def print_summary(self):
        """打印结果摘要"""
        print("\n📋 === 操作结果摘要 ===")
        
        if not self.results:
            print("   没有操作结果")
            return
        
        for device_name, result in self.results.items():
            print(f"\n🔧 设备: {device_name}")
            
            if 'connection' in result:
                conn_status = {
                    'success': '✅ 连接成功',
                    'failed': '❌ 连接失败', 
                    'error': '⚠️ 连接错误'
                }.get(result['connection'], '❓ 未知状态')
                print(f"   连接状态: {conn_status}")
            
            if 'configuration' in result:
                config_status = {
                    'success': '✅ 配置成功',
                    'failed': '❌ 配置失败'
                }.get(result['configuration'], '❓ 未知状态')
                print(f"   配置状态: {config_status}")
            
            if 'cleanup' in result:
                cleanup_status = {
                    'success': '✅ 清理成功',
                    'failed': '❌ 清理失败'
                }.get(result['cleanup'], '❓ 未知状态')
                print(f"   清理状态: {cleanup_status}")
            
            if 'sensor_groups' in result:
                sensor_status = "✅ 已配置" if result['sensor_groups'] else "❌ 未配置"
                dest_status = "✅ 已配置" if result['destination_groups'] else "❌ 未配置"
                sub_status = "✅ 已配置" if result.get('subscriptions', False) else "❌ 未配置"
                print(f"   传感器组: {sensor_status}")
                print(f"   目标组: {dest_status}")
                print(f"   订阅配置: {sub_status}")
            
            if result.get('error'):
                print(f"   错误信息: {result['error']}")
            
            if 'timestamp' in result:
                print(f"   最后更新: {result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    def save_results(self, filename=None):
        """保存结果到文件"""
        if filename is None:
            filename = f"telemetry_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        import json
        
        # 转换 datetime 对象为字符串
        serializable_results = {}
        for device, result in self.results.items():
            serializable_results[device] = {}
            for key, value in result.items():
                if isinstance(value, datetime):
                    serializable_results[device][key] = value.isoformat()
                else:
                    serializable_results[device][key] = value
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 结果已保存到: {filename}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='统一的 Telemetry 配置管理器')
    parser.add_argument('action', choices=['test', 'configure', 'check', 'clean'], 
                       help='操作类型: test=测试连接, configure=配置telemetry, check=检查状态, clean=清理配置')
    parser.add_argument('--devices', nargs='+', help='指定设备名称（默认为所有设备）')
    parser.add_argument('--workers', type=int, default=3, help='并发工作线程数（默认3）')
    parser.add_argument('--save', action='store_true', help='保存结果到文件')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging("INFO")
    
    # 创建管理器
    manager = TelemetryConfigManager()
    
    print("🚀 统一 Telemetry 配置管理器")
    print("=" * 60)
    
    # 显示可用设备
    available_devices = manager.get_available_devices()
    print(f"📱 可用设备: {', '.join(available_devices)}")
    
    # 确定要操作的设备
    target_devices = args.devices if args.devices else available_devices
    print(f"🎯 目标设备: {', '.join(target_devices)}")
    print()
    
    try:
        # 执行操作
        manager.batch_operation(args.action, target_devices, args.workers)
        
        # 显示摘要
        manager.print_summary()
        
        # 保存结果
        if args.save:
            manager.save_results()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return 1
    finally:
        try:
            manager.client.disconnect_all()
        except:
            pass

if __name__ == "__main__":
    sys.exit(main())
