#!/usr/bin/env python3
"""
Telemetry 配置管理器
整合所有监控功能，提供统一的配置接口
"""

import logging
import yaml
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum

from .netconf_client import NetconfClient
from .yang_templates import YangTemplates


class MonitoringLevel(Enum):
    """监控级别枚举"""
    BASIC = "basic"
    ADVANCED = "advanced"
    ENTERPRISE = "enterprise"
    PERFORMANCE = "performance"
    CUSTOM = "custom"


@dataclass
class CollectorConfig:
    """收集器配置"""
    name: str
    address: str
    port: int = 57400
    protocol: str = "grpc"
    encoding: str = "json"


@dataclass
class SensorGroupConfig:
    """传感器组配置"""
    group_id: str
    category: str
    metrics: List[str]
    paths: List[str] = field(default_factory=list)
    description: str = ""


@dataclass
class SubscriptionConfig:
    """订阅配置"""
    subscription_id: str
    sensor_group: str
    destination_group: str
    sample_interval: int = 30000  # 毫秒
    source_address: str = ""
    qos_marking: int = 0
    suppress_redundant: bool = False
    heartbeat_interval: int = 60000  # 毫秒


class TelemetryConfigurator:
    """Telemetry 配置管理器"""
    
    def __init__(self, device_config_file: Optional[str] = None, 
                 telemetry_config_file: Optional[str] = None):
        """
        初始化 Telemetry 配置管理器
        
        Args:
            device_config_file: 设备配置文件路径
            telemetry_config_file: Telemetry 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.netconf_client = NetconfClient(device_config_file)
        self.yang_templates = YangTemplates()
        
        # 配置存储
        self.collectors: Dict[str, CollectorConfig] = {}
        self.sensor_groups: Dict[str, SensorGroupConfig] = {}
        self.subscriptions: Dict[str, SubscriptionConfig] = {}
        self.monitoring_templates: Dict[str, Dict[str, Any]] = {}
        self.monitoring_categories: Dict[str, List[str]] = {}
        
        # 加载配置
        if telemetry_config_file:
            self.load_telemetry_config(telemetry_config_file)
        
        # 初始化预定义的监控指标
        self._init_predefined_metrics()
    
    def load_telemetry_config(self, config_file: str) -> None:
        """
        加载 Telemetry 配置文件
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 加载收集器配置
            if 'collectors' in config:
                for collector_data in config['collectors']:
                    collector = CollectorConfig(**collector_data)
                    self.collectors[collector.name] = collector
                    self.logger.info(f"已加载收集器配置: {collector.name}")
            
            # 加载监控模板
            if 'monitoring_templates' in config:
                self.monitoring_templates = config['monitoring_templates']
                self.logger.info("已加载监控模板配置")
            
            # 加载监控类别
            if 'monitoring_categories' in config:
                self.monitoring_categories = config['monitoring_categories']
                self.logger.info("已加载监控类别配置")
                
        except Exception as e:
            self.logger.error(f"加载 Telemetry 配置文件失败: {e}")
            raise
    
    def _init_predefined_metrics(self) -> None:
        """初始化预定义的监控指标"""
        # 如果没有从配置文件加载，使用默认配置
        if not self.monitoring_categories:
            self.monitoring_categories = {
                'system_basic': ['cpu_utilization', 'memory_utilization'],
                'system_all': ['cpu_utilization', 'memory_utilization', 'storage_utilization', 'system_temperature'],
                'system_performance': ['cpu_detailed', 'memory_detailed', 'process_stats'],
                'interface_basic': ['interface_traffic', 'interface_status'],
                'interface_all': ['interface_traffic', 'interface_status', 'interface_errors', 'optical_stats'],
                'interface_performance': ['interface_detailed_stats', 'interface_queue_stats'],
                'routing_protocols': ['bgp_neighbors', 'ospf_neighbors', 'isis_adjacency', 'routing_table_stats'],
                'hardware_monitoring': ['fan_status', 'power_supply', 'temperature_sensors', 'hardware_alarms'],
                'qos_monitoring': ['queue_statistics', 'policy_statistics', 'traffic_classification', 'bandwidth_utilization'],
                'mpls_vpn': ['lsp_statistics', 'vrf_statistics', 'mpls_labels', 'vpn_connections']
            }
        
        if not self.monitoring_templates:
            self.monitoring_templates = {
                'basic': {
                    'description': '基础系统和接口监控',
                    'categories': ['system_basic', 'interface_basic'],
                    'sample_interval': 30000
                },
                'advanced': {
                    'description': '包含路由协议的高级监控',
                    'categories': ['system_all', 'interface_all', 'routing_protocols'],
                    'sample_interval': 15000
                },
                'enterprise': {
                    'description': '全面的企业级监控',
                    'categories': ['system_all', 'interface_all', 'routing_protocols', 'hardware_monitoring', 'qos_monitoring', 'mpls_vpn'],
                    'sample_interval': 10000
                },
                'performance': {
                    'description': '专注于性能指标的监控',
                    'categories': ['system_performance', 'interface_performance', 'qos_monitoring'],
                    'sample_interval': 5000
                }
            }
    
    def add_collector(self, name: str, address: str, port: int = 57400, 
                     protocol: str = "grpc", encoding: str = "json") -> None:
        """
        添加收集器配置
        
        Args:
            name: 收集器名称
            address: 收集器地址
            port: 收集器端口
            protocol: 协议类型
            encoding: 编码格式
        """
        collector = CollectorConfig(
            name=name,
            address=address,
            port=port,
            protocol=protocol,
            encoding=encoding
        )
        self.collectors[name] = collector
        self.logger.info(f"已添加收集器: {name} ({address}:{port})")
    
    def create_sensor_group(self, group_id: str, category: str, 
                           metrics: List[str], description: str = "") -> SensorGroupConfig:
        """
        创建传感器组
        
        Args:
            group_id: 传感器组 ID
            category: 监控类别
            metrics: 监控指标列表
            description: 描述
            
        Returns:
            传感器组配置
        """
        # 收集所有路径
        all_paths = []
        for metric in metrics:
            try:
                paths = self.yang_templates.get_sensor_paths(category, metric)
                all_paths.extend(paths)
            except ValueError as e:
                self.logger.warning(f"跳过无效指标 {metric}: {e}")
        
        sensor_group = SensorGroupConfig(
            group_id=group_id,
            category=category,
            metrics=metrics,
            paths=all_paths,
            description=description
        )
        
        self.sensor_groups[group_id] = sensor_group
        self.logger.info(f"已创建传感器组: {group_id} (类别: {category}, 指标数: {len(metrics)})")
        
        return sensor_group
    
    def create_sensor_groups_from_template(self, template_name: str) -> List[SensorGroupConfig]:
        """
        根据模板创建传感器组
        
        Args:
            template_name: 模板名称
            
        Returns:
            创建的传感器组列表
        """
        if template_name not in self.monitoring_templates:
            raise ValueError(f"未知的监控模板: {template_name}")
        
        template = self.monitoring_templates[template_name]
        sensor_groups = []
        
        for category in template['categories']:
            if category not in self.monitoring_categories:
                self.logger.warning(f"跳过未知的监控类别: {category}")
                continue
            
            metrics = self.monitoring_categories[category]
            group_id = f"{template_name}_{category}"
            
            sensor_group = self.create_sensor_group(
                group_id=group_id,
                category=self._get_category_from_name(category),
                metrics=metrics,
                description=f"{template['description']} - {category}"
            )
            sensor_groups.append(sensor_group)
        
        return sensor_groups
    
    def _get_category_from_name(self, category_name: str) -> str:
        """从类别名称获取 YANG 模板类别"""
        if category_name.startswith('system'):
            return 'system'
        elif category_name.startswith('interface'):
            return 'interface'
        elif category_name.startswith('routing'):
            return 'routing'
        elif category_name.startswith('hardware'):
            return 'hardware'
        elif category_name.startswith('qos'):
            return 'qos'
        elif category_name.startswith('mpls'):
            return 'mpls'
        else:
            return 'system'  # 默认类别
    
    def create_subscription(self, subscription_id: str, sensor_group: str,
                          destination_group: str, sample_interval: int = 30000,
                          **kwargs) -> SubscriptionConfig:
        """
        创建订阅配置
        
        Args:
            subscription_id: 订阅 ID
            sensor_group: 传感器组名称
            destination_group: 目标组名称
            sample_interval: 采样间隔（毫秒）
            **kwargs: 其他配置参数
            
        Returns:
            订阅配置
        """
        subscription = SubscriptionConfig(
            subscription_id=subscription_id,
            sensor_group=sensor_group,
            destination_group=destination_group,
            sample_interval=sample_interval,
            source_address=kwargs.get('source_address', ''),
            qos_marking=kwargs.get('qos_marking', 0),
            suppress_redundant=kwargs.get('suppress_redundant', False),
            heartbeat_interval=kwargs.get('heartbeat_interval', 60000)
        )
        
        self.subscriptions[subscription_id] = subscription
        self.logger.info(f"已创建订阅: {subscription_id}")
        
        return subscription
    
    def configure_telemetry(self, device_name: str, template_name: str = "basic",
                          collector_name: str = "default-collector",
                          sample_interval: Optional[int] = None) -> bool:
        """
        配置设备的 Telemetry
        
        Args:
            device_name: 设备名称
            template_name: 监控模板名称
            collector_name: 收集器名称
            sample_interval: 采样间隔（覆盖模板默认值）
            
        Returns:
            配置是否成功
        """
        try:
            # 验证收集器存在
            if collector_name not in self.collectors:
                raise ValueError(f"收集器 '{collector_name}' 不存在")
            
            collector = self.collectors[collector_name]
            
            # 根据模板创建传感器组
            sensor_groups = self.create_sensor_groups_from_template(template_name)
            
            # 获取模板配置
            template = self.monitoring_templates[template_name]
            interval = sample_interval or template.get('sample_interval', 30000)
            
            # 准备配置数据
            sensor_groups_config = []
            subscriptions_config = []
            
            for sg in sensor_groups:
                sensor_groups_config.append({
                    'group_id': sg.group_id,
                    'paths': sg.paths
                })
                
                # 为每个传感器组创建订阅
                subscription_id = f"sub_{sg.group_id}"
                subscription = self.create_subscription(
                    subscription_id=subscription_id,
                    sensor_group=sg.group_id,
                    destination_group=collector_name,
                    sample_interval=interval
                )
                
                subscriptions_config.append({
                    'subscription_id': subscription.subscription_id,
                    'sensor_group': subscription.sensor_group,
                    'destination_group': subscription.destination_group,
                    'sample_interval': subscription.sample_interval,
                    'source_address': subscription.source_address,
                    'qos_marking': subscription.qos_marking,
                    'suppress_redundant': str(subscription.suppress_redundant).lower(),
                    'heartbeat_interval': subscription.heartbeat_interval
                })
            
            # 目标组配置
            destination_groups_config = [{
                'group_id': collector_name,
                'destinations': [{
                    'address': collector.address,
                    'port': collector.port,
                    'protocol': collector.protocol,
                    'encoding': collector.encoding
                }]
            }]
            
            # 生成完整的 XML 配置
            config_xml = self.yang_templates.generate_complete_telemetry_config(
                sensor_groups=sensor_groups_config,
                destination_groups=destination_groups_config,
                subscriptions=subscriptions_config
            )
            
            # 应用配置到设备
            self.logger.info(f"正在配置设备 '{device_name}' 的 Telemetry...")
            self.netconf_client.edit_config(device_name, config_xml)
            self.netconf_client.commit(device_name)
            
            self.logger.info(f"设备 '{device_name}' Telemetry 配置成功")
            return True
            
        except Exception as e:
            self.logger.error(f"配置设备 '{device_name}' Telemetry 失败: {e}")
            return False
    
    def configure_custom_telemetry(self, device_name: str, 
                                 categories: List[str],
                                 collector_address: str,
                                 collector_port: int = 57400,
                                 sample_interval: int = 30000) -> bool:
        """
        配置自定义 Telemetry 监控
        
        Args:
            device_name: 设备名称
            categories: 监控类别列表
            collector_address: 收集器地址
            collector_port: 收集器端口
            sample_interval: 采样间隔
            
        Returns:
            配置是否成功
        """
        try:
            # 创建临时收集器
            collector_name = f"custom_{device_name}"
            self.add_collector(collector_name, collector_address, collector_port)
            
            # 创建传感器组
            sensor_groups_config = []
            subscriptions_config = []
            
            for category in categories:
                if category not in self.monitoring_categories:
                    self.logger.warning(f"跳过未知的监控类别: {category}")
                    continue
                
                metrics = self.monitoring_categories[category]
                group_id = f"custom_{category}"
                
                # 创建传感器组
                sensor_group = self.create_sensor_group(
                    group_id=group_id,
                    category=self._get_category_from_name(category),
                    metrics=metrics,
                    description=f"自定义监控 - {category}"
                )
                
                sensor_groups_config.append({
                    'group_id': sensor_group.group_id,
                    'paths': sensor_group.paths
                })
                
                # 创建订阅
                subscription_id = f"sub_{group_id}"
                subscription = self.create_subscription(
                    subscription_id=subscription_id,
                    sensor_group=group_id,
                    destination_group=collector_name,
                    sample_interval=sample_interval
                )
                
                subscriptions_config.append({
                    'subscription_id': subscription.subscription_id,
                    'sensor_group': subscription.sensor_group,
                    'destination_group': subscription.destination_group,
                    'sample_interval': subscription.sample_interval,
                    'source_address': subscription.source_address,
                    'qos_marking': subscription.qos_marking,
                    'suppress_redundant': str(subscription.suppress_redundant).lower(),
                    'heartbeat_interval': subscription.heartbeat_interval
                })
            
            # 目标组配置
            collector = self.collectors[collector_name]
            destination_groups_config = [{
                'group_id': collector_name,
                'destinations': [{
                    'address': collector.address,
                    'port': collector.port,
                    'protocol': collector.protocol,
                    'encoding': collector.encoding
                }]
            }]
            
            # 生成并应用配置
            config_xml = self.yang_templates.generate_complete_telemetry_config(
                sensor_groups=sensor_groups_config,
                destination_groups=destination_groups_config,
                subscriptions=subscriptions_config
            )
            
            self.logger.info(f"正在配置设备 '{device_name}' 的自定义 Telemetry...")
            self.netconf_client.edit_config(device_name, config_xml)
            self.netconf_client.commit(device_name)
            
            self.logger.info(f"设备 '{device_name}' 自定义 Telemetry 配置成功")
            return True
            
        except Exception as e:
            self.logger.error(f"配置设备 '{device_name}' 自定义 Telemetry 失败: {e}")
            return False
    
    def get_telemetry_status(self, device_name: str) -> Dict[str, Any]:
        """
        获取设备 Telemetry 状态
        
        Args:
            device_name: 设备名称
            
        Returns:
            Telemetry 状态信息
        """
        try:
            # 获取当前配置
            config_xml = self.netconf_client.get_config(device_name)
            
            # 这里可以解析 XML 来提取 Telemetry 状态
            # 简化实现，返回基本信息
            return {
                'device': device_name,
                'configured': True,
                'sensor_groups': len(self.sensor_groups),
                'subscriptions': len(self.subscriptions),
                'collectors': len(self.collectors)
            }
            
        except Exception as e:
            self.logger.error(f"获取设备 '{device_name}' Telemetry 状态失败: {e}")
            return {
                'device': device_name,
                'configured': False,
                'error': str(e)
            }
    
    def list_available_templates(self) -> List[str]:
        """获取所有可用的监控模板"""
        return list(self.monitoring_templates.keys())
    
    def list_available_categories(self) -> List[str]:
        """获取所有可用的监控类别"""
        return list(self.monitoring_categories.keys())
    
    def get_template_info(self, template_name: str) -> Dict[str, Any]:
        """
        获取模板详细信息
        
        Args:
            template_name: 模板名称
            
        Returns:
            模板信息
        """
        if template_name not in self.monitoring_templates:
            raise ValueError(f"未知的监控模板: {template_name}")
        
        template = self.monitoring_templates[template_name]
        
        # 计算总的监控指标数量
        total_metrics = 0
        for category in template['categories']:
            if category in self.monitoring_categories:
                total_metrics += len(self.monitoring_categories[category])
        
        return {
            'name': template_name,
            'description': template.get('description', ''),
            'categories': template['categories'],
            'sample_interval': template.get('sample_interval', 30000),
            'total_metrics': total_metrics
        }
    
    def validate_configuration(self, device_name: str) -> Dict[str, Any]:
        """
        验证设备配置
        
        Args:
            device_name: 设备名称
            
        Returns:
            验证结果
        """
        results = {
            'device_connection': False,
            'telemetry_support': False,
            'configuration_valid': False,
            'errors': []
        }
        
        try:
            # 验证设备连接
            if self.netconf_client.validate_connection(device_name):
                results['device_connection'] = True
                
                # 检查设备能力
                capabilities = self.netconf_client.get_device_capabilities(device_name)
                telemetry_supported = any('telemetry' in cap.lower() for cap in capabilities)
                results['telemetry_support'] = telemetry_supported
                
                if not telemetry_supported:
                    results['errors'].append("设备不支持 OpenConfig Telemetry")
                
                # 验证当前配置
                try:
                    config_xml = self.netconf_client.get_config(device_name)
                    results['configuration_valid'] = True
                except Exception as e:
                    results['errors'].append(f"获取配置失败: {e}")
            else:
                results['errors'].append("设备连接失败")
                
        except Exception as e:
            results['errors'].append(f"验证过程出错: {e}")
        
        return results
    
    def cleanup(self) -> None:
        """清理资源"""
        self.netconf_client.disconnect_all()
        self.logger.info("Telemetry 配置管理器已清理")