#!/usr/bin/env python3
"""
高级使用示例
演示 OpenConfig Telemetry Monitor 的高级功能
"""

import sys
import os
import time

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.telemetry_config import TelemetryConfigurator
from src.utils import setup_logging, ProgressBar


def main():
    """高级使用示例"""
    
    # 设置调试日志
    setup_logging("DEBUG", "advanced_example.log")
    
    print("=== OpenConfig Telemetry Monitor 高级使用示例 ===\n")
    
    try:
        # 1. 初始化配置器
        print("1. 初始化 Telemetry 配置器...")
        configurator = TelemetryConfigurator(
            device_config_file="../config/device_config.yaml",
            telemetry_config_file="../config/telemetry_config.yaml"
        )
        print("✓ 配置器初始化成功\n")
        
        # 2. 高级收集器配置
        print("2. 配置多个收集器...")
        collectors = [
            {"name": "primary-collector", "address": "*************", "port": 57400},
            {"name": "backup-collector", "address": "*************", "port": 57400},
            {"name": "analytics-collector", "address": "*************", "port": 57401}
        ]
        
        for collector in collectors:
            configurator.add_collector(**collector)
            print(f"  ✓ 添加收集器: {collector['name']} ({collector['address']}:{collector['port']})")
        print()
        
        # 3. 自定义监控类别组合
        print("3. 创建自定义监控配置...")
        
        # 高性能监控配置
        performance_categories = ["system_performance", "interface_performance", "qos_monitoring"]
        print(f"  高性能监控类别: {', '.join(performance_categories)}")
        
        # 网络协议监控配置
        protocol_categories = ["routing_protocols", "mpls_vpn"]
        print(f"  协议监控类别: {', '.join(protocol_categories)}")
        
        # 硬件健康监控配置
        hardware_categories = ["hardware_monitoring", "system_all"]
        print(f"  硬件监控类别: {', '.join(hardware_categories)}")
        print()
        
        # 4. 批量传感器组创建
        print("4. 批量创建传感器组...")
        
        sensor_groups = []
        
        # 为每个类别创建传感器组
        all_categories = {
            "performance": performance_categories,
            "protocol": protocol_categories,
            "hardware": hardware_categories
        }
        
        for group_type, categories in all_categories.items():
            for category in categories:
                if category in configurator.monitoring_categories:
                    metrics = configurator.monitoring_categories[category]
                    group_id = f"advanced_{group_type}_{category}"
                    
                    sg = configurator.create_sensor_group(
                        group_id=group_id,
                        category=configurator._get_category_from_name(category),
                        metrics=metrics,
                        description=f"高级{group_type}监控 - {category}"
                    )
                    sensor_groups.append(sg)
                    print(f"  ✓ 创建传感器组: {sg.group_id} ({len(sg.paths)} 个路径)")
        print()
        
        # 5. 创建多个订阅配置
        print("5. 创建多个订阅配置...")
        
        subscriptions = []
        
        # 为不同的传感器组创建不同采样间隔的订阅
        subscription_configs = [
            {"group_type": "performance", "collector": "primary-collector", "interval": 5000},
            {"group_type": "protocol", "collector": "analytics-collector", "interval": 15000},
            {"group_type": "hardware", "collector": "backup-collector", "interval": 60000}
        ]
        
        for config in subscription_configs:
            # 找到对应类型的传感器组
            matching_groups = [sg for sg in sensor_groups if config["group_type"] in sg.group_id]
            
            for sg in matching_groups:
                subscription_id = f"sub_{sg.group_id}"
                subscription = configurator.create_subscription(
                    subscription_id=subscription_id,
                    sensor_group=sg.group_id,
                    destination_group=config["collector"],
                    sample_interval=config["interval"]
                )
                subscriptions.append(subscription)
                print(f"  ✓ 创建订阅: {subscription.subscription_id} -> {config['collector']} ({config['interval']}ms)")
        print()
        
        # 6. 生成完整的 XML 配置
        print("6. 生成完整的 XML 配置...")
        
        # 准备配置数据
        sensor_groups_config = []
        for sg in sensor_groups:
            sensor_groups_config.append({
                'group_id': sg.group_id,
                'paths': sg.paths
            })
        
        destination_groups_config = []
        for collector_name, collector in configurator.collectors.items():
            destination_groups_config.append({
                'group_id': collector_name,
                'destinations': [{
                    'address': collector.address,
                    'port': collector.port,
                    'protocol': collector.protocol,
                    'encoding': collector.encoding
                }]
            })
        
        subscriptions_config = []
        for sub in subscriptions:
            subscriptions_config.append({
                'subscription_id': sub.subscription_id,
                'sensor_group': sub.sensor_group,
                'destination_group': sub.destination_group,
                'sample_interval': sub.sample_interval,
                'source_address': sub.source_address,
                'qos_marking': sub.qos_marking,
                'suppress_redundant': str(sub.suppress_redundant).lower(),
                'heartbeat_interval': sub.heartbeat_interval
            })
        
        # 生成 XML 配置
        xml_config = configurator.yang_templates.generate_complete_telemetry_config(
            sensor_groups=sensor_groups_config,
            destination_groups=destination_groups_config,
            subscriptions=subscriptions_config
        )
        
        print(f"  ✓ 生成完整 XML 配置 ({len(xml_config)} 字符)")
        
        # 保存配置到文件
        config_file = "generated_telemetry_config.xml"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(xml_config)
        print(f"  ✓ 配置已保存到: {config_file}")
        print()
        
        # 7. 模拟批量设备配置
        print("7. 模拟批量设备配置...")
        
        devices = configurator.netconf_client.list_devices()
        if devices:
            print(f"  找到 {len(devices)} 个设备，开始模拟配置...")
            
            progress = ProgressBar(len(devices), "配置进度")
            
            for device_name in devices:
                # 模拟配置过程
                time.sleep(0.5)  # 模拟配置时间
                
                try:
                    # 这里可以调用实际的配置方法
                    # success = configurator.configure_custom_telemetry(...)
                    
                    # 模拟成功
                    print(f"\n    ✓ 设备 '{device_name}' 配置成功")
                    
                except Exception as e:
                    print(f"\n    ✗ 设备 '{device_name}' 配置失败: {e}")
                
                progress.update()
            
            progress.finish()
        else:
            print("  未找到配置的设备")
        print()
        
        # 8. 配置验证和统计
        print("8. 配置验证和统计...")
        
        print(f"  传感器组总数: {len(sensor_groups)}")
        print(f"  订阅总数: {len(subscriptions)}")
        print(f"  收集器总数: {len(configurator.collectors)}")
        
        # 统计监控路径
        total_paths = sum(len(sg.paths) for sg in sensor_groups)
        print(f"  监控路径总数: {total_paths}")
        
        # 按类别统计
        category_stats = {}
        for sg in sensor_groups:
            category = sg.category
            if category not in category_stats:
                category_stats[category] = {'groups': 0, 'paths': 0}
            category_stats[category]['groups'] += 1
            category_stats[category]['paths'] += len(sg.paths)
        
        print("  按类别统计:")
        for category, stats in category_stats.items():
            print(f"    {category}: {stats['groups']} 组, {stats['paths']} 路径")
        print()
        
        # 9. 清理资源
        print("9. 清理资源...")
        configurator.cleanup()
        print("✓ 资源清理完成")
        
        print("\n=== 高级使用示例完成 ===")
        print(f"详细日志已保存到: advanced_example.log")
        print(f"生成的配置文件: {config_file}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())