# gRPC Telemetry 收集器使用指南

## 问题背景

Nokia 设备的 telemetry 配置默认使用 `STREAM_GRPC` 协议和 `ENC_PROTO3` 编码，这与传统的 TCP socket 收集器不兼容。设备会连接到收集器但立即断开，因为协议不匹配。

## 解决方案

我们创建了一个支持 gRPC 协议的 telemetry 收集器 `grpc_telemetry_collector.py`，它能够：

1. 接受 gRPC 连接
2. 解析 gRPC 消息格式
3. 处理压缩和非压缩数据
4. 保存 telemetry 数据到 JSON 文件

## 使用方法

### 1. 选择合适的收集器

我们提供了多个收集器来处理不同的协议：

```bash
# 选项 1: 原始 gRPC 收集器 (推荐)
python raw_grpc_collector.py

# 选项 2: 简单多协议收集器
python simple_telemetry_server.py

# 选项 3: 基础 gRPC 收集器
python grpc_telemetry_collector.py

# 选项 4: 传统 TCP 收集器
python telemetry_collector.py
```

**推荐使用 `raw_grpc_collector.py`**，它能够处理 Nokia 设备的 HTTP/2 gRPC 连接。

### 2. 配置设备

```bash
# 配置所有设备的 telemetry
python telemetry_config.py configure

# 或者配置单个设备
python telemetry_config.py configure --devices nokia-alu-1
```

### 3. 检查配置状态

```bash
# 检查所有设备的配置状态
python telemetry_config.py check
```

应该看到所有项目都显示 ✅：
- 传感器组: ✅ 已配置
- 目标组: ✅ 已配置  
- 订阅配置: ✅ 已配置

### 4. 监控数据收集

gRPC 收集器会显示：
- 新连接信息
- 接收到的消息数量
- 数据保存状态
- 定期状态报告

## 配置详情

### 当前配置参数

- **目标地址**: ***************:57400
- **协议**: STREAM_GRPC (设备自动选择)
- **编码**: ENC_PROTO3 (设备自动选择)
- **采样间隔**: 5000ms (5秒)
- **心跳间隔**: 15000ms (15秒)

### 监控的传感器路径

1. `/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state`
2. `/openconfig-platform:components/component/state`
3. `/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state`
4. `/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state`
5. `/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state`
6. `/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization`
7. `/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state`

## 数据存储

收集到的数据保存在 `telemetry_data/` 目录下：
- 文件格式: `{device_name}_{YYYYMMDD}.json`
- 每行一个 JSON 对象
- 包含时间戳、源IP、消息长度等元数据

## 故障排除

### 设备连接但立即断开

**原因**: 协议不匹配
**解决**: 使用 `grpc_telemetry_collector.py` 而不是 `telemetry_collector.py`

### 没有收到数据

1. 检查配置状态: `python telemetry_config.py check`
2. 确认目标地址正确 (***************:57400)
3. 检查网络连通性
4. 查看收集器日志

### 配置失败

1. 检查设备连接: `python telemetry_config.py test`
2. 查看详细错误信息
3. 确认 NETCONF 连接正常

## 命令参考

```bash
# 测试设备连接
python telemetry_config.py test

# 配置 telemetry
python telemetry_config.py configure [--devices device1 device2]

# 检查配置状态  
python telemetry_config.py check [--devices device1 device2]

# 清理配置 (注意: 可能因设备限制而失败)
python telemetry_config.py clean [--devices device1 device2]

# 启动 gRPC 收集器
python grpc_telemetry_collector.py

# 启动传统 TCP 收集器 (不推荐用于 Nokia 设备)
python telemetry_collector.py
```

## 注意事项

1. **协议兼容性**: Nokia 设备默认使用 gRPC，必须使用对应的收集器
2. **数据格式**: gRPC 数据可能是二进制格式，需要特殊处理
3. **网络配置**: 确保防火墙允许 57400 端口的连接
4. **存储空间**: telemetry 数据量可能很大，注意磁盘空间

## 更新历史

- 2025-08-05: 创建 gRPC 收集器，解决协议兼容性问题
- 2025-08-05: 基于 odccAllonePsg-e.xml 更新配置模板
- 2025-08-05: 修复订阅检查逻辑和目标组配置
