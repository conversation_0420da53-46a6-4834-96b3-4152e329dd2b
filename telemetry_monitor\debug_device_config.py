#!/usr/bin/env python3
"""
调试设备配置的脚本 - 查看实际的协议和编码设置
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

from src.netconf_client import NetconfClient
from src.utils import setup_logging

def debug_device_config():
    """调试设备配置"""
    
    # 设置日志
    setup_logging("INFO")
    
    # 创建客户端
    client = NetconfClient("config/device_config.yaml")
    device_name = "nokia-alu-3"  # 使用刚刚连接的设备
    
    print(f"🔍 调试设备配置 - 设备: {device_name}")
    
    try:
        with client.get_connection(device_name) as conn:
            # 获取完整的 telemetry 配置
            print("\n1. 获取完整的 telemetry 配置...")
            filter_xml = '''
            <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
              <sensor-groups/>
              <destination-groups/>
              <subscriptions/>
            </telemetry-system>
            '''
            
            current_config = conn.get_config(source='running', filter=('subtree', filter_xml))
            config_str = str(current_config)
            
            print("=" * 80)
            print("完整配置:")
            print(config_str)
            print("=" * 80)
            
            # 分析协议和编码
            print("\n2. 分析协议和编码设置...")
            
            if 'STREAM_GRPC' in config_str:
                print("✅ 发现 STREAM_GRPC 协议")
            if 'STREAM_TCP' in config_str:
                print("✅ 发现 STREAM_TCP 协议")
            if 'ENC_PROTO3' in config_str:
                print("✅ 发现 ENC_PROTO3 编码")
            if 'ENC_JSON_IETF' in config_str:
                print("✅ 发现 ENC_JSON_IETF 编码")
            
            # 查找订阅配置
            print("\n3. 分析订阅配置...")
            if 'allPsg' in config_str:
                print("✅ 找到订阅 allPsg")
                # 提取订阅相关配置
                lines = config_str.split('\n')
                in_subscription = False
                for line in lines:
                    if '<name>allPsg</name>' in line:
                        in_subscription = True
                    elif in_subscription and '</persistent-subscription>' in line:
                        in_subscription = False
                    elif in_subscription:
                        if 'protocol' in line or 'encoding' in line or 'local-source-address' in line:
                            print(f"   {line.strip()}")
            
            # 查找目标组配置
            print("\n4. 分析目标组配置...")
            if '***************' in config_str:
                print("✅ 找到目标组 ***************")
                # 提取目标组相关配置
                lines = config_str.split('\n')
                in_destination = False
                for line in lines:
                    if '<group-id>***************</group-id>' in line:
                        in_destination = True
                    elif in_destination and '</destination-group>' in line:
                        in_destination = False
                    elif in_destination:
                        if 'destination-address' in line or 'destination-port' in line:
                            print(f"   {line.strip()}")
                            
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
    
    finally:
        try:
            client.disconnect_all()
        except:
            pass

if __name__ == "__main__":
    debug_device_config()
