#!/usr/bin/env python3
"""
实时 Telemetry 监控器
专门用于实时显示从设备接收的 telemetry 数据
"""

import socket
import json
import threading
import time
from datetime import datetime
import sys

class RealTimeTelemetryMonitor:
    """实时 Telemetry 监控器"""
    
    def __init__(self, host='0.0.0.0', port=57400):
        self.host = host
        self.port = port
        self.running = False
        self.server_socket = None
        self.clients = []
        self.data_count = 0
        self.start_time = None
        
    def start(self):
        """启动监控器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(10)
            
            self.running = True
            self.start_time = datetime.now()
            
            print("🚀 实时 Telemetry 监控器启动")
            print(f"📡 监听地址: {self.host}:{self.port}")
            print(f"⏰ 启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("🔍 等待设备连接...")
            print("=" * 80)
            
            # 启动状态报告线程
            status_thread = threading.Thread(target=self._status_reporter)
            status_thread.daemon = True
            status_thread.start()
            
            # 主循环
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    
                    print(f"\n🔗 新连接: {client_address}")
                    print(f"⏰ 连接时间: {datetime.now().strftime('%H:%M:%S')}")
                    
                    self.clients.append(client_socket)
                    
                    # 为每个客户端启动处理线程
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ 接受连接失败: {e}")
                    break
                    
        except Exception as e:
            print(f"❌ 启动监控器失败: {e}")
            return False
    
    def _handle_client(self, client_socket, client_address):
        """处理客户端数据"""
        buffer = ""
        
        try:
            while self.running:
                data = client_socket.recv(8192)
                if not data:
                    break
                
                try:
                    decoded_data = data.decode('utf-8')
                    buffer += decoded_data
                    
                    # 处理完整的消息
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        if line.strip():
                            self._process_message(line.strip(), client_address)
                            
                except UnicodeDecodeError:
                    # 处理二进制数据
                    self._process_binary_message(data, client_address)
                
        except Exception as e:
            print(f"❌ 处理客户端 {client_address} 数据失败: {e}")
        
        finally:
            try:
                client_socket.close()
                if client_socket in self.clients:
                    self.clients.remove(client_socket)
                print(f"🔌 客户端 {client_address} 断开连接")
            except:
                pass
    
    def _process_message(self, data, client_address):
        """处理文本消息"""
        self.data_count += 1
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        
        print(f"\n📊 [{timestamp}] 消息 #{self.data_count} 来自 {client_address}")
        print("=" * 80)
        
        # 尝试解析 JSON
        try:
            json_data = json.loads(data)
            print("📋 JSON 数据:")
            self._display_json_data(json_data)
            
        except json.JSONDecodeError:
            print("📝 原始文本数据:")
            print(data)
        
        print("=" * 80)
        
        # 保存数据
        self._save_data(data, timestamp, client_address)
    
    def _process_binary_message(self, data, client_address):
        """处理二进制消息"""
        self.data_count += 1
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        
        print(f"\n📊 [{timestamp}] 二进制消息 #{self.data_count} 来自 {client_address}")
        print("=" * 80)
        print(f"📏 数据长度: {len(data)} 字节")
        print(f"🔍 数据预览: {data[:100]}...")
        print("=" * 80)
    
    def _display_json_data(self, data):
        """显示 JSON 数据"""
        # 基本信息
        if 'device' in data:
            print(f"📱 设备: {data['device']}")
        
        if 'timestamp' in data:
            print(f"⏰ 设备时间戳: {data['timestamp']}")
        
        if 'sensor_group' in data:
            print(f"📡 传感器组: {data['sensor_group']}")
        
        # 数值指标
        numeric_metrics = {}
        for key, value in data.items():
            if isinstance(value, (int, float)):
                numeric_metrics[key] = value
        
        if numeric_metrics:
            print("\n📈 数值指标:")
            for key, value in numeric_metrics.items():
                if isinstance(value, float):
                    print(f"   {key}: {value:.2f}")
                else:
                    print(f"   {key}: {value}")
        
        # 字符串字段
        string_fields = {}
        for key, value in data.items():
            if isinstance(value, str) and key not in ['device', 'sensor_group']:
                string_fields[key] = value
        
        if string_fields:
            print("\n📝 字符串字段:")
            for key, value in string_fields.items():
                print(f"   {key}: {value}")
        
        # 嵌套对象
        nested_objects = {}
        for key, value in data.items():
            if isinstance(value, dict):
                nested_objects[key] = value
        
        if nested_objects:
            print("\n📦 嵌套对象:")
            for key, value in nested_objects.items():
                print(f"   {key}: {len(value)} 个字段")
                # 显示嵌套对象的前几个字段
                for i, (sub_key, sub_value) in enumerate(list(value.items())[:3]):
                    if isinstance(sub_value, (int, float)):
                        print(f"     {sub_key}: {sub_value}")
                    else:
                        print(f"     {sub_key}: {str(sub_value)[:50]}...")
                if len(value) > 3:
                    print(f"     ... 还有 {len(value) - 3} 个字段")
        
        # 数组字段
        array_fields = {}
        for key, value in data.items():
            if isinstance(value, list):
                array_fields[key] = value
        
        if array_fields:
            print("\n📋 数组字段:")
            for key, value in array_fields.items():
                print(f"   {key}: {len(value)} 个元素")
                if value and len(value) > 0:
                    print(f"     第一个元素: {str(value[0])[:50]}...")
        
        # 完整 JSON（如果数据较小）
        json_str = json.dumps(data, indent=2, ensure_ascii=False)
        if len(json_str) < 1000:
            print(f"\n📄 完整 JSON:")
            print(json_str)
        else:
            print(f"\n📄 JSON 大小: {len(json_str)} 字符 (太大，不完整显示)")
    
    def _save_data(self, data, timestamp, client_address):
        """保存数据到文件"""
        try:
            filename = f"realtime_telemetry_{datetime.now().strftime('%Y%m%d')}.log"
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {client_address}: {data}\n")
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def _status_reporter(self):
        """状态报告线程"""
        while self.running:
            time.sleep(30)  # 每30秒报告一次
            if self.running:
                runtime = datetime.now() - self.start_time
                print(f"\n📊 状态报告 - 运行时间: {runtime}, 活跃连接: {len(self.clients)}, 已接收: {self.data_count} 条消息")
    
    def stop(self):
        """停止监控器"""
        self.running = False
        
        for client in self.clients:
            try:
                client.close()
            except:
                pass
        self.clients.clear()
        
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        print("🛑 监控器已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='实时 Telemetry 监控器')
    parser.add_argument('--host', default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=57400, help='监听端口')
    
    args = parser.parse_args()
    
    monitor = RealTimeTelemetryMonitor(args.host, args.port)
    
    try:
        monitor.start()
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        monitor.stop()
    except Exception as e:
        print(f"监控器运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
