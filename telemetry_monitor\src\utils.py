#!/usr/bin/env python3
"""
工具函数模块
提供通用的工具函数和日志配置
"""

import logging
import logging.config
import os
import sys
import json
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional, List
from datetime import datetime
import colorlog


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> None:
    """
    设置日志配置
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径（可选）
    """
    # 日志格式配置
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    color_format = "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s%(reset)s"
    
    # 基础配置
    handlers = []
    
    # 控制台处理器（带颜色）
    console_handler = colorlog.StreamHandler()
    console_handler.setFormatter(colorlog.ColoredFormatter(
        color_format,
        datefmt='%Y-%m-%d %H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    ))
    handlers.append(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(
            log_format,
            datefmt='%Y-%m-%d %H:%M:%S'
        ))
        handlers.append(file_handler)
    
    # 配置根日志记录器
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        handlers=handlers,
        format=log_format
    )
    
    # 设置第三方库的日志级别
    logging.getLogger('ncclient').setLevel(logging.WARNING)
    logging.getLogger('paramiko').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def validate_ip_address(ip: str) -> bool:
    """
    验证 IP 地址格式
    
    Args:
        ip: IP 地址字符串
        
    Returns:
        是否为有效的 IP 地址
    """
    import ipaddress
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False


def validate_port(port: int) -> bool:
    """
    验证端口号
    
    Args:
        port: 端口号
        
    Returns:
        是否为有效的端口号
    """
    return 1 <= port <= 65535


def format_xml(xml_string: str, indent: str = "  ") -> str:
    """
    格式化 XML 字符串
    
    Args:
        xml_string: XML 字符串
        indent: 缩进字符
        
    Returns:
        格式化后的 XML 字符串
    """
    try:
        root = ET.fromstring(xml_string)
        ET.indent(root, space=indent)
        return ET.tostring(root, encoding='unicode')
    except ET.ParseError:
        return xml_string


def parse_xml_response(xml_string: str) -> Dict[str, Any]:
    """
    解析 XML 响应
    
    Args:
        xml_string: XML 字符串
        
    Returns:
        解析后的字典
    """
    try:
        root = ET.fromstring(xml_string)
        return xml_to_dict(root)
    except ET.ParseError as e:
        return {"error": f"XML 解析错误: {e}"}


def xml_to_dict(element: ET.Element) -> Dict[str, Any]:
    """
    将 XML 元素转换为字典
    
    Args:
        element: XML 元素
        
    Returns:
        字典表示
    """
    result = {}
    
    # 添加属性
    if element.attrib:
        result['@attributes'] = element.attrib
    
    # 添加文本内容
    if element.text and element.text.strip():
        if len(element) == 0:
            return element.text.strip()
        result['#text'] = element.text.strip()
    
    # 添加子元素
    for child in element:
        child_data = xml_to_dict(child)
        
        if child.tag in result:
            # 如果已存在同名标签，转换为列表
            if not isinstance(result[child.tag], list):
                result[child.tag] = [result[child.tag]]
            result[child.tag].append(child_data)
        else:
            result[child.tag] = child_data
    
    return result


def save_config_to_file(config: Dict[str, Any], file_path: str) -> None:
    """
    保存配置到文件
    
    Args:
        config: 配置字典
        file_path: 文件路径
    """
    # 确保目录存在
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)
    
    # 根据文件扩展名选择格式
    _, ext = os.path.splitext(file_path)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        if ext.lower() in ['.yaml', '.yml']:
            import yaml
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        elif ext.lower() == '.json':
            json.dump(config, f, ensure_ascii=False, indent=2)
        else:
            # 默认使用 YAML 格式
            import yaml
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)


def load_config_from_file(file_path: str) -> Dict[str, Any]:
    """
    从文件加载配置
    
    Args:
        file_path: 文件路径
        
    Returns:
        配置字典
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"配置文件不存在: {file_path}")
    
    _, ext = os.path.splitext(file_path)
    
    with open(file_path, 'r', encoding='utf-8') as f:
        if ext.lower() in ['.yaml', '.yml']:
            import yaml
            return yaml.safe_load(f)
        elif ext.lower() == '.json':
            return json.load(f)
        else:
            # 尝试 YAML 格式
            import yaml
            return yaml.safe_load(f)


def format_bytes(bytes_value: int) -> str:
    """
    格式化字节数为人类可读格式
    
    Args:
        bytes_value: 字节数
        
    Returns:
        格式化后的字符串
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.2f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.2f} PB"


def format_duration(seconds: int) -> str:
    """
    格式化持续时间
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化后的时间字符串
    """
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{minutes}分{remaining_seconds}秒"
    elif seconds < 86400:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{hours}小时{remaining_minutes}分钟"
    else:
        days = seconds // 86400
        remaining_hours = (seconds % 86400) // 3600
        return f"{days}天{remaining_hours}小时"


def generate_unique_id(prefix: str = "") -> str:
    """
    生成唯一 ID
    
    Args:
        prefix: ID 前缀
        
    Returns:
        唯一 ID 字符串
    """
    import uuid
    unique_id = str(uuid.uuid4()).replace('-', '')[:8]
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    
    if prefix:
        return f"{prefix}_{timestamp}_{unique_id}"
    else:
        return f"{timestamp}_{unique_id}"


def retry_on_exception(max_retries: int = 3, delay: float = 1.0, 
                      exceptions: tuple = (Exception,)):
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试间隔（秒）
        exceptions: 需要重试的异常类型
    """
    import time
    import functools
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        logging.warning(f"函数 {func.__name__} 执行失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                        time.sleep(delay)
                    else:
                        logging.error(f"函数 {func.__name__} 执行失败，已达到最大重试次数")
            
            raise last_exception
        
        return wrapper
    return decorator


def validate_telemetry_config(config: Dict[str, Any]) -> List[str]:
    """
    验证 Telemetry 配置
    
    Args:
        config: 配置字典
        
    Returns:
        错误信息列表
    """
    errors = []
    
    # 验证收集器配置
    if 'collectors' in config:
        for i, collector in enumerate(config['collectors']):
            if 'name' not in collector:
                errors.append(f"收集器 {i} 缺少 name 字段")
            if 'address' not in collector:
                errors.append(f"收集器 {i} 缺少 address 字段")
            elif not validate_ip_address(collector['address']):
                errors.append(f"收集器 {i} 的 address 不是有效的 IP 地址")
            
            port = collector.get('port', 57400)
            if not validate_port(port):
                errors.append(f"收集器 {i} 的端口号无效: {port}")
    
    # 验证监控模板
    if 'monitoring_templates' in config:
        for template_name, template in config['monitoring_templates'].items():
            if 'categories' not in template:
                errors.append(f"监控模板 '{template_name}' 缺少 categories 字段")
            
            sample_interval = template.get('sample_interval', 30000)
            if not isinstance(sample_interval, int) or sample_interval <= 0:
                errors.append(f"监控模板 '{template_name}' 的 sample_interval 无效")
    
    return errors


def create_backup_filename(original_file: str) -> str:
    """
    创建备份文件名
    
    Args:
        original_file: 原始文件路径
        
    Returns:
        备份文件路径
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    name, ext = os.path.splitext(original_file)
    return f"{name}_backup_{timestamp}{ext}"


def ensure_directory_exists(directory: str) -> None:
    """
    确保目录存在
    
    Args:
        directory: 目录路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory)
        logging.info(f"已创建目录: {directory}")


def get_system_info() -> Dict[str, Any]:
    """
    获取系统信息
    
    Returns:
        系统信息字典
    """
    import platform
    import psutil
    
    return {
        'platform': platform.platform(),
        'python_version': platform.python_version(),
        'cpu_count': psutil.cpu_count(),
        'memory_total': psutil.virtual_memory().total,
        'disk_usage': psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent
    }


class ProgressBar:
    """简单的进度条类"""
    
    def __init__(self, total: int, description: str = "Progress"):
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = datetime.now()
    
    def update(self, increment: int = 1) -> None:
        """更新进度"""
        self.current += increment
        self._display()
    
    def _display(self) -> None:
        """显示进度条"""
        if self.total == 0:
            return
        
        percent = (self.current / self.total) * 100
        bar_length = 50
        filled_length = int(bar_length * self.current // self.total)
        
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        elapsed = datetime.now() - self.start_time
        elapsed_str = str(elapsed).split('.')[0]  # 去掉微秒
        
        sys.stdout.write(f'\r{self.description}: |{bar}| {percent:.1f}% ({self.current}/{self.total}) - {elapsed_str}')
        sys.stdout.flush()
        
        if self.current >= self.total:
            print()  # 换行
    
    def finish(self) -> None:
        """完成进度条"""
        self.current = self.total
        self._display()


def print_table(data: List[Dict[str, Any]], headers: Optional[List[str]] = None) -> None:
    """
    打印表格
    
    Args:
        data: 数据列表
        headers: 表头列表
    """
    if not data:
        print("无数据")
        return
    
    # 确定表头
    if headers is None:
        headers = list(data[0].keys())
    
    # 计算列宽
    col_widths = {}
    for header in headers:
        col_widths[header] = len(str(header))
        for row in data:
            if header in row:
                col_widths[header] = max(col_widths[header], len(str(row[header])))
    
    # 打印表头
    header_row = " | ".join(str(header).ljust(col_widths[header]) for header in headers)
    print(header_row)
    print("-" * len(header_row))
    
    # 打印数据行
    for row in data:
        data_row = " | ".join(str(row.get(header, "")).ljust(col_widths[header]) for header in headers)
        print(data_row)


def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    截断字符串
    
    Args:
        text: 原始字符串
        max_length: 最大长度
        suffix: 后缀
        
    Returns:
        截断后的字符串
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix