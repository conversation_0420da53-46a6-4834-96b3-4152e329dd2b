# 📊 统一 Telemetry 监控系统使用指南

## 🎯 项目重构说明

项目已按照要求进行了重构和简化：

### ✅ 完成的重构
1. **文件整理** - 不需要的文件已移动到 `backup/` 文件夹
2. **统一配置** - 所有 telemetry 配置合并为一个文件
3. **统一收集器** - 一个收集器支持单个或多个设备
4. **按设备分文件** - 数据按时间和设备名称保存到独立文件

## 📁 当前项目结构

### 🔧 核心文件
- `telemetry_config.py` - **统一的 Telemetry 配置管理器**
- `telemetry_collector.py` - **统一的 Telemetry 收集器**
- `test_data_sender.py` - 测试数据发送器
- `data_analyzer.py` - 数据分析工具

### 📂 配置文件
- `config/device_config.yaml` - 设备连接配置
- `config/telemetry_config.yaml` - Telemetry 配置模板

### 📦 备份文件
- `backup/` - 包含所有不再使用的文件

## 🚀 使用方法

### 1. **配置设备 Telemetry**

#### 测试设备连接
```bash
# 测试所有设备连接
python telemetry_config.py test

# 测试指定设备
python telemetry_config.py test --devices nokia-alu-1 nokia-alu-2
```

#### 配置 Telemetry
```bash
# 配置所有设备
python telemetry_config.py configure

# 配置指定设备
python telemetry_config.py configure --devices nokia-alu-1

# 配置并保存结果
python telemetry_config.py configure --save
```

#### 检查配置状态
```bash
# 检查所有设备状态
python telemetry_config.py check

# 检查指定设备
python telemetry_config.py check --devices nokia-alu-1 nokia-alu-2
```

#### 清理配置
```bash
# 清理所有设备配置
python telemetry_config.py clean

# 清理指定设备
python telemetry_config.py clean --devices nokia-alu-1
```

### 2. **启动数据收集**

#### 启动收集器
```bash
# 使用默认设置启动
python telemetry_collector.py

# 指定监听地址和端口
python telemetry_collector.py --host 0.0.0.0 --port 57400

# 指定数据保存目录
python telemetry_collector.py --data-dir /path/to/telemetry_data
```

#### 收集器特性
- ✅ **自动设备识别** - 根据数据中的设备名称自动分类
- ✅ **按设备分文件** - 每个设备的数据保存到独立文件
- ✅ **按日期分文件** - 文件名格式：`设备名_YYYYMMDD.json`
- ✅ **实时显示** - 显示接收到的数据和关键信息
- ✅ **状态报告** - 每30秒显示设备活跃状态

### 3. **测试数据流**

#### 发送测试数据
```bash
# 发送5条测试消息
python test_data_sender.py test

# 指定设备和数量
python test_data_sender.py test --device nokia-alu-2 --count 10

# 指定收集器地址
python test_data_sender.py test --collector-ip *************** --collector-port 57400
```

#### 持续发送数据
```bash
# 每10秒发送一次数据
python test_data_sender.py continuous

# 指定发送间隔
python test_data_sender.py continuous --interval 5

# 指定设备名称
python test_data_sender.py continuous --device nokia-alu-3 --interval 15
```

### 4. **数据分析**

#### 分析收集的数据
```bash
# 查看数据摘要
python data_analyzer.py --summary

# 查看所有数据
python data_analyzer.py --all

# 分析指定文件
python data_analyzer.py --file telemetry_data/nokia-alu-1_20250805.json --all

# 导出分析结果
python data_analyzer.py --export analysis_results.csv
```

## 📊 数据文件结构

### 数据保存位置
```
telemetry_data/
├── nokia-alu-1_20250805.json    # Nokia ALU 设备1的数据
├── nokia-alu-2_20250805.json    # Nokia ALU 设备2的数据
├── cisco-device-1_20250805.json # Cisco 设备的数据
└── unknown-192.168.1.100_20250805.json  # 未知设备的数据
```

### 数据文件格式
每行一个 JSON 记录：
```json
{
  "timestamp": "2025-08-05T15:30:45.123456",
  "device": "nokia-alu-1",
  "client_address": "***************:12345",
  "message_count": 15,
  "data": {
    "timestamp": 1704441045123,
    "device": "nokia-alu-1",
    "sensor_group": "allSg",
    "subscription": "telemetry-monitoring",
    "sequence": 15,
    "data": {
      "openconfig-platform:components": {
        // 完整的 telemetry 数据
      }
    }
  }
}
```

## 🔍 实时监控显示

### 收集器输出示例
```
🌟 统一 Telemetry 收集器
📡 监听地址: 0.0.0.0:57400
📁 数据目录: telemetry_data
⏰ 启动时间: 2025-08-05 15:30:00
🔍 等待设备连接...
================================================================================

🔗 新连接: ('***************', 12345)
⏰ 连接时间: 15:30:05

📊 [15:30:05.123] 设备: nokia-alu-1 | 消息 #1 | 来自: ('***************', 12345)
================================================================================
📡 传感器组: allSg
📋 订阅: telemetry-monitoring
🔢 序列号: 1
⏰ 设备时间戳: 1704441045123
💻 CPU 利用率: 25.5%
🔌 输出功率: -2.5 dBm
🌡️ 温度: 42.5 °C
📡 频率: 196100000 MHz
🔧 组件: 收发器: 1, CPU: 1
📏 数据大小: 2048 字节
------------------------------------------------------------

📊 === Telemetry 收集器状态报告 ===
⏱️  运行时间: 0:05:30
📨 总消息数: 25
🔗 活跃连接: 2
📱 活跃设备: 2

📋 设备详情:
   nokia-alu-1: 🟢 活跃 | 消息: 15 | 文件: nokia-alu-1_20250805.json
     首次: 15:30:05 | 最后: 15:35:30
   nokia-alu-2: 🟢 活跃 | 消息: 10 | 文件: nokia-alu-2_20250805.json
     首次: 15:32:10 | 最后: 15:35:25
==================================================
```

## 🎯 典型使用流程

### 完整的监控设置流程
```bash
# 1. 配置设备 telemetry
python telemetry_config.py configure --save

# 2. 启动收集器
python telemetry_collector.py --data-dir telemetry_data

# 3. 测试数据流（在另一个终端）
python test_data_sender.py test --device nokia-alu-1

# 4. 分析收集的数据
python data_analyzer.py --file telemetry_data/nokia-alu-1_20250805.json --all
```

## 🔧 配置说明

### 设备配置 (config/device_config.yaml)
```yaml
devices:
  - name: "nokia-alu-1"
    host: "***************"
    port: 830
    username: "admin"
    password: "Nokia#123"
    device_type: "alu"
```

### Telemetry 配置 (config/telemetry_config.yaml)
```yaml
collector:
  name: "telemetry-collector"
  address: "***************"
  port: 57400
  data_directory: "telemetry_data"
```

## 📈 监控的数据类型

系统会自动提取和显示以下关键信息：
- 💻 **CPU 利用率** - 实时 CPU 使用情况
- 🔌 **光功率** - 输入/输出光功率
- 🌡️ **温度** - 设备温度信息
- 📡 **频率** - 光载波频率
- 🔧 **组件统计** - 收发器、光通道、逻辑通道等数量

## 🚫 清理和维护

### 清理设备配置
```bash
# 清理所有设备的 telemetry 配置
python telemetry_config.py clean

# 清理指定设备
python telemetry_config.py clean --devices nokia-alu-1
```

### 数据文件管理
- 数据文件按日期自动分割
- 可以定期归档或删除旧的数据文件
- 建议定期备份重要的监控数据

这个重构后的系统提供了统一、简化的 telemetry 监控解决方案，支持单个或多个设备，数据按设备和时间自动分类保存。
