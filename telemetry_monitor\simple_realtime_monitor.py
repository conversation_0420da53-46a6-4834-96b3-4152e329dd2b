#!/usr/bin/env python3
"""
简化的实时 Telemetry 监控器
专门用于实际设备调试
"""

import socket
import json
import time
from datetime import datetime

def start_monitor():
    """启动监控器"""
    
    host = '0.0.0.0'
    port = 57400
    
    print("🚀 启动实时 Telemetry 监控器")
    print(f"📡 监听地址: {host}:{port}")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔍 等待设备连接...")
    print("=" * 80)
    
    try:
        # 创建服务器套接字
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_socket.bind((host, port))
        server_socket.listen(10)
        
        data_count = 0
        
        while True:
            try:
                # 接受连接
                client_socket, client_address = server_socket.accept()
                print(f"\n🔗 新连接: {client_address}")
                print(f"⏰ 连接时间: {datetime.now().strftime('%H:%M:%S')}")
                
                # 处理数据
                buffer = ""
                
                while True:
                    try:
                        data = client_socket.recv(4096)
                        if not data:
                            break
                        
                        decoded_data = data.decode('utf-8')
                        buffer += decoded_data
                        
                        # 处理完整的消息
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            if line.strip():
                                data_count += 1
                                process_message(line.strip(), client_address, data_count)
                    
                    except Exception as e:
                        print(f"❌ 处理数据失败: {e}")
                        break
                
                client_socket.close()
                print(f"🔌 客户端 {client_address} 断开连接")
                
            except Exception as e:
                print(f"❌ 处理连接失败: {e}")
                
    except Exception as e:
        print(f"❌ 启动监控器失败: {e}")
    
    finally:
        try:
            server_socket.close()
        except:
            pass

def process_message(data, client_address, count):
    """处理消息"""
    
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    
    print(f"\n📊 [{timestamp}] 消息 #{count} 来自 {client_address}")
    print("=" * 80)
    
    # 尝试解析 JSON
    try:
        json_data = json.loads(data)
        print("📋 JSON 数据:")
        display_json_data(json_data)
        
    except json.JSONDecodeError:
        print("📝 原始文本数据:")
        print(data)
    
    print("=" * 80)
    
    # 保存数据
    save_data(data, timestamp, client_address)

def display_json_data(data):
    """显示 JSON 数据"""
    
    # 基本信息
    if 'device' in data:
        print(f"📱 设备: {data['device']}")
    
    if 'timestamp' in data:
        print(f"⏰ 设备时间戳: {data['timestamp']}")
    
    if 'sensor_group' in data:
        print(f"📡 传感器组: {data['sensor_group']}")
    
    # 数值指标
    numeric_metrics = {}
    for key, value in data.items():
        if isinstance(value, (int, float)):
            numeric_metrics[key] = value
    
    if numeric_metrics:
        print("\n📈 数值指标:")
        for key, value in numeric_metrics.items():
            if isinstance(value, float):
                print(f"   {key}: {value:.2f}")
            else:
                print(f"   {key}: {value}")
    
    # 嵌套对象
    nested_objects = {}
    for key, value in data.items():
        if isinstance(value, dict):
            nested_objects[key] = value
    
    if nested_objects:
        print("\n📦 嵌套对象:")
        for key, value in nested_objects.items():
            print(f"   {key}: {len(value)} 个字段")
            # 显示嵌套对象的前几个字段
            for i, (sub_key, sub_value) in enumerate(list(value.items())[:3]):
                if isinstance(sub_value, (int, float)):
                    print(f"     {sub_key}: {sub_value}")
                else:
                    print(f"     {sub_key}: {str(sub_value)[:50]}...")
            if len(value) > 3:
                print(f"     ... 还有 {len(value) - 3} 个字段")
    
    # 完整 JSON（如果数据较小）
    json_str = json.dumps(data, indent=2, ensure_ascii=False)
    if len(json_str) < 500:
        print(f"\n📄 完整 JSON:")
        print(json_str)
    else:
        print(f"\n📄 JSON 大小: {len(json_str)} 字符 (太大，不完整显示)")

def save_data(data, timestamp, client_address):
    """保存数据到文件"""
    try:
        filename = f"realtime_telemetry_{datetime.now().strftime('%Y%m%d')}.log"
        with open(filename, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {client_address}: {data}\n")
    except Exception as e:
        print(f"❌ 保存数据失败: {e}")

if __name__ == "__main__":
    try:
        start_monitor()
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，监控器已停止")
    except Exception as e:
        print(f"❌ 监控器运行失败: {e}")
